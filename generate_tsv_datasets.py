#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
从numpy数据生成TSV格式的训练数据集
"""

import os
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split

def load_numpy_data():
    """加载numpy格式的数据"""
    print("📖 加载numpy数据...")
    
    data_dir = "/home/<USER>/ET-BERT/output/result/dataset/"
    
    try:
        x_train = np.load(data_dir + "x_datagram_train.npy", allow_pickle=True)
        x_test = np.load(data_dir + "x_datagram_test.npy", allow_pickle=True)
        x_valid = np.load(data_dir + "x_datagram_valid.npy", allow_pickle=True)
        
        y_train = np.load(data_dir + "y_train.npy", allow_pickle=True)
        y_test = np.load(data_dir + "y_test.npy", allow_pickle=True)
        y_valid = np.load(data_dir + "y_valid.npy", allow_pickle=True)
        
        print(f"  ✅ 训练集: {len(x_train)} 样本")
        print(f"  ✅ 测试集: {len(x_test)} 样本")
        print(f"  ✅ 验证集: {len(x_valid)} 样本")
        
        return (x_train, y_train), (x_test, y_test), (x_valid, y_valid)
        
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return None

def save_tsv_dataset(x_data, y_data, output_path, include_labels=True):
    """保存TSV格式数据集"""
    print(f"💾 保存TSV数据集: {output_path}")
    
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            if include_labels:
                f.write("label\ttext_a\n")
                for i in range(len(x_data)):
                    label = int(y_data[i])
                    text = str(x_data[i]).replace('\t', ' ').replace('\n', ' ')
                    f.write(f"{label}\t{text}\n")
            else:
                f.write("text_a\n")
                for i in range(len(x_data)):
                    text = str(x_data[i]).replace('\t', ' ').replace('\n', ' ')
                    f.write(f"{text}\n")
        
        print(f"  ✅ 保存完成: {len(x_data)} 样本")
        return True
        
    except Exception as e:
        print(f"  ❌ 保存失败: {e}")
        return False

def generate_tsv_datasets():
    """生成所有TSV数据集"""
    print("🔄 生成TSV格式数据集...")
    
    # 加载数据
    data = load_numpy_data()
    if data is None:
        return False
    
    (x_train, y_train), (x_test, y_test), (x_valid, y_valid) = data
    
    # 输出目录
    output_dir = "/home/<USER>/ET-BERT/output/datasets/"
    
    # 保存训练集
    if not save_tsv_dataset(x_train, y_train, output_dir + "train_dataset.tsv"):
        return False
    
    # 保存验证集
    if not save_tsv_dataset(x_valid, y_valid, output_dir + "valid_dataset.tsv"):
        return False
    
    # 保存测试集（带标签）
    if not save_tsv_dataset(x_test, y_test, output_dir + "test_dataset.tsv"):
        return False
    
    # 保存测试集（无标签，用于推理）
    if not save_tsv_dataset(x_test, y_test, output_dir + "nolabel_test_dataset.tsv", include_labels=False):
        return False
    
    print("✅ 所有TSV数据集生成完成!")
    
    # 显示数据集统计
    print("\n📊 数据集统计:")
    datasets = {
        "训练集": output_dir + "train_dataset.tsv",
        "验证集": output_dir + "valid_dataset.tsv",
        "测试集": output_dir + "test_dataset.tsv",
        "推理集": output_dir + "nolabel_test_dataset.tsv"
    }
    
    for name, path in datasets.items():
        if os.path.exists(path):
            with open(path, 'r') as f:
                line_count = sum(1 for line in f) - 1  # 减去标题行
            file_size = os.path.getsize(path) / (1024*1024)  # MB
            print(f"  {name}: {line_count:,} 样本, {file_size:.2f} MB")
    
    return True

def main():
    print("🚀 TSV数据集生成器")
    print("=" * 50)
    
    if generate_tsv_datasets():
        print("\n🎉 TSV数据集生成成功!")
        print("现在可以运行训练脚本:")
        print("  python train_and_evaluate.py")
        return 0
    else:
        print("\n❌ TSV数据集生成失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
