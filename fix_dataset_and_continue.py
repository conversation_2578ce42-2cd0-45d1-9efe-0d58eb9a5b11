#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
修复数据集问题并继续处理：
1. 删除样本数为0的类别
2. 重新映射标签ID
3. 创建输出目录
4. 生成TSV文件
"""

import os
import json
import numpy as np
import csv
from sklearn.model_selection import train_test_split

def load_and_filter_dataset():
    """加载数据集并过滤掉样本数为0的类别"""
    print("📖 加载并过滤数据集...")
    
    dataset_path = "/home/<USER>/ET-BERT/output/result/dataset.json"
    
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    print("🔍 原始数据集统计:")
    valid_classes = {}
    empty_classes = []
    
    for label, data in dataset.items():
        sample_count = data.get('samples', 0)
        if sample_count > 0:
            valid_classes[label] = data
            print(f"  ✅ {label}: {sample_count:,} 个样本")
        else:
            empty_classes.append(label)
            print(f"  ❌ {label}: {sample_count} 个样本 (将删除)")
    
    print(f"\n📊 过滤结果:")
    print(f"  有效类别: {len(valid_classes)} 个")
    print(f"  删除类别: {len(empty_classes)} 个 ({', '.join(empty_classes)})")
    
    return valid_classes, empty_classes

def create_label_mapping(valid_classes):
    """创建新的标签映射"""
    print("\n🏷️  创建新的标签映射...")
    
    # 按类别名称排序以确保一致性
    sorted_classes = sorted(valid_classes.keys())
    
    # 创建新的ID映射
    old_to_new_mapping = {}
    new_to_name_mapping = {}
    
    for new_id, class_name in enumerate(sorted_classes):
        old_id = int(class_name)  # 假设原始标签就是类别ID
        old_to_new_mapping[old_id] = new_id
        new_to_name_mapping[new_id] = class_name
    
    print("📋 标签映射表:")
    for old_id, new_id in old_to_new_mapping.items():
        class_name = new_to_name_mapping[new_id]
        sample_count = valid_classes[str(old_id)]['samples']
        print(f"  {old_id} → {new_id}: {class_name} ({sample_count:,} 样本)")
    
    return old_to_new_mapping, new_to_name_mapping

def extract_and_remap_data(valid_classes, old_to_new_mapping):
    """提取数据并重新映射标签"""
    print("\n📦 提取并重新映射数据...")
    
    X_data = []
    y_data = []
    
    for old_label_str, class_data in valid_classes.items():
        old_label = int(old_label_str)
        new_label = old_to_new_mapping[old_label]
        
        # 提取payload数据
        payload_data = class_data.get('payload', {})
        sample_count = 0
        
        for sample_key, sample_value in payload_data.items():
            X_data.append(sample_value)
            y_data.append(new_label)
            sample_count += 1
        
        print(f"  ✅ 类别 {old_label} → {new_label}: 提取了 {sample_count:,} 个样本")
    
    print(f"\n📊 数据提取完成:")
    print(f"  总样本数: {len(X_data):,}")
    print(f"  总类别数: {len(set(y_data))}")
    
    return X_data, y_data

def split_and_save_data(X_data, y_data):
    """分割数据并保存为numpy格式"""
    print("\n✂️  分割数据集...")
    
    # 分割数据：训练80%，测试20%
    X_train_val, X_test, y_train_val, y_test = train_test_split(
        X_data, y_data, test_size=0.2, random_state=42, stratify=y_data
    )
    
    # 再分割训练集：训练80%，验证20%
    X_train, X_val, y_train, y_val = train_test_split(
        X_train_val, y_train_val, test_size=0.2, random_state=42, stratify=y_train_val
    )
    
    print(f"📊 数据分割结果:")
    print(f"  训练集: {len(X_train):,} 样本")
    print(f"  验证集: {len(X_val):,} 样本")
    print(f"  测试集: {len(X_test):,} 样本")
    
    # 保存numpy格式
    dataset_dir = "/home/<USER>/ET-BERT/output/result/dataset/"
    os.makedirs(dataset_dir, exist_ok=True)
    
    print("\n💾 保存numpy格式数据...")
    
    np.save(dataset_dir + "x_datagram_train.npy", np.array(X_train))
    np.save(dataset_dir + "x_datagram_valid.npy", np.array(X_val))
    np.save(dataset_dir + "x_datagram_test.npy", np.array(X_test))
    
    np.save(dataset_dir + "y_train.npy", np.array(y_train))
    np.save(dataset_dir + "y_valid.npy", np.array(y_val))
    np.save(dataset_dir + "y_test.npy", np.array(y_test))
    
    print("  ✅ numpy文件保存完成")
    
    return (X_train, y_train), (X_val, y_val), (X_test, y_test)

def create_tsv_files(train_data, val_data, test_data):
    """创建TSV格式文件"""
    print("\n📄 创建TSV格式文件...")
    
    # 创建输出目录
    tsv_dir = "/home/<USER>/ET-BERT/output/datasets/"
    os.makedirs(tsv_dir, exist_ok=True)
    
    datasets = {
        "train": train_data,
        "valid": val_data,
        "test": test_data
    }
    
    for dataset_type, (X, y) in datasets.items():
        file_path = os.path.join(tsv_dir, f"{dataset_type}_dataset.tsv")
        
        print(f"  📝 创建 {dataset_type}_dataset.tsv...")
        
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f, delimiter='\t')
            
            # 写入标题行
            writer.writerow(['label', 'text_a'])
            
            # 写入数据
            for i in range(len(X)):
                # 确保文本数据是字符串格式
                text_data = str(X[i]).replace('\t', ' ').replace('\n', ' ')
                writer.writerow([y[i], text_data])
        
        print(f"    ✅ {dataset_type}_dataset.tsv 创建完成 ({len(X):,} 样本)")
    
    # 创建无标签测试集（用于推理）
    nolabel_path = os.path.join(tsv_dir, "nolabel_test_dataset.tsv")
    print(f"  📝 创建 nolabel_test_dataset.tsv...")
    
    X_test, _ = test_data
    with open(nolabel_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f, delimiter='\t')
        writer.writerow(['text_a'])
        
        for text in X_test:
            text_data = str(text).replace('\t', ' ').replace('\n', ' ')
            writer.writerow([text_data])
    
    print(f"    ✅ nolabel_test_dataset.tsv 创建完成")

def update_config_files(num_classes, new_to_name_mapping):
    """更新配置文件中的类别数"""
    print(f"\n⚙️  更新配置文件...")
    
    # 更新main.py中的类别数
    main_py_path = "data_process/main.py"
    
    try:
        with open(main_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换类别数
        content = content.replace('_category = 18', f'_category = {num_classes}')
        
        with open(main_py_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"  ✅ 更新 main.py: 类别数 18 → {num_classes}")
        
    except Exception as e:
        print(f"  ⚠️  更新main.py失败: {e}")
    
    # 保存类别映射文件
    mapping_path = "/home/<USER>/ET-BERT/output/result/class_mapping.json"
    mapping_data = {
        'num_classes': num_classes,
        'id_to_name': new_to_name_mapping,
        'name_to_id': {name: id for id, name in new_to_name_mapping.items()}
    }
    
    with open(mapping_path, 'w', encoding='utf-8') as f:
        json.dump(mapping_data, f, indent=2, ensure_ascii=False)
    
    print(f"  ✅ 保存类别映射到: {mapping_path}")

def main():
    print("🚀 数据集修复和处理器")
    print("=" * 60)
    
    try:
        # 1. 加载并过滤数据集
        valid_classes, empty_classes = load_and_filter_dataset()
        
        # 2. 创建标签映射
        old_to_new_mapping, new_to_name_mapping = create_label_mapping(valid_classes)
        num_classes = len(new_to_name_mapping)
        
        # 3. 提取并重新映射数据
        X_data, y_data = extract_and_remap_data(valid_classes, old_to_new_mapping)
        
        # 4. 分割并保存数据
        train_data, val_data, test_data = split_and_save_data(X_data, y_data)
        
        # 5. 创建TSV文件
        create_tsv_files(train_data, val_data, test_data)
        
        # 6. 更新配置文件
        update_config_files(num_classes, new_to_name_mapping)
        
        print(f"\n🎉 数据集修复完成!")
        print(f"📊 最终统计:")
        print(f"  有效类别数: {num_classes}")
        print(f"  总样本数: {len(X_data):,}")
        print(f"  删除的类别: {len(empty_classes)} 个")
        
        print(f"\n📋 下一步:")
        print(f"  1. 运行训练: python train_with_verbose_output.py")
        print(f"  2. 类别数已更新为: {num_classes}")
        print(f"  3. 预期准确率: 显著提升")
        
        return 0
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
