#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
NetMamba数据集适配ET-BERT的专用脚本
针对 /home/<USER>/NetMamba/dataset/database_clean 数据集
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def check_dataset_structure():
    """检查数据集结构"""
    dataset_path = "/home/<USER>/NetMamba/dataset/database_clean"
    
    print("🔍 检查数据集结构...")
    
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return False, 0
    
    # 统计类别和文件数量
    class_dirs = []
    total_files = 0
    
    for item in os.listdir(dataset_path):
        item_path = os.path.join(dataset_path, item)
        if os.path.isdir(item_path):
            class_dirs.append(item)
            # 统计该类别下的pcap文件数量
            pcap_files = []
            for root, dirs, files in os.walk(item_path):
                for file in files:
                    if file.endswith(('.pcap', '.pcapng')):
                        pcap_files.append(file)
            
            print(f"  📁 类别 '{item}': {len(pcap_files)} 个PCAP文件")
            total_files += len(pcap_files)
    
    print(f"✅ 总共发现 {len(class_dirs)} 个类别，{total_files} 个PCAP文件")
    
    if len(class_dirs) == 0:
        print("❌ 未发现任何类别文件夹")
        return False, 0
    
    return True, len(class_dirs)

def setup_output_directories():
    """创建输出目录"""
    print("📁 创建输出目录...")
    
    output_dirs = [
        "/home/<USER>/ET-BERT/output",
        "/home/<USER>/ET-BERT/output/result",
        "/home/<USER>/ET-BERT/output/datasets",
        "/home/<USER>/ET-BERT/output/corpora"
    ]
    
    for dir_path in output_dirs:
        os.makedirs(dir_path, exist_ok=True)
        print(f"  ✅ {dir_path}")

def run_data_preprocessing(num_classes, samples_per_class, force_regenerate=False):
    """运行数据预处理"""
    print("🔄 开始数据预处理...")

    # 如果需要强制重新生成，删除旧的数据集文件
    if force_regenerate:
        old_files = [
            "/home/<USER>/ET-BERT/output/result/dataset.json",
            "/home/<USER>/ET-BERT/output/result/dataset/",
        ]

        for file_path in old_files:
            if os.path.exists(file_path):
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    print(f"  🗑️  删除旧文件: {file_path}")
                elif os.path.isdir(file_path):
                    import shutil
                    shutil.rmtree(file_path)
                    print(f"  🗑️  删除旧目录: {file_path}")

    # 修改data_process/main.py中的类别数量
    main_py_path = "data_process/main.py"
    
    # 读取文件内容
    with open(main_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换类别数量
    content = content.replace(f'_category = 120', f'_category = {num_classes}')
    content = content.replace(f'samples = [1000]', f'samples = [{samples_per_class}]')
    
    # 写回文件
    with open(main_py_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"  ✅ 已设置类别数量为 {num_classes}")
    print(f"  ✅ 已设置每类样本数为 {samples_per_class}")
    
    # 运行数据处理
    print("  🔄 执行数据处理...")
    print("  📊 这可能需要几分钟时间，请耐心等待...")

    try:
        # 使用实时输出的方式运行
        process = subprocess.Popen(
            [sys.executable, "data_process/main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )

        print("  📝 实时输出:")
        output_lines = []

        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(f"    {output.strip()}")
                output_lines.append(output.strip())

        return_code = process.poll()

        if return_code == 0:
            print("  ✅ 数据预处理完成")
            return True
        else:
            print("  ❌ 数据预处理失败")
            print(f"  返回码: {return_code}")
            return False

    except Exception as e:
        print(f"  ❌ 执行出错: {e}")
        return False

def run_model_training(num_classes):
    """运行模型训练"""
    print("🤖 开始模型训练...")
    
    # 检查TSV文件是否存在
    tsv_files = [
        "/home/<USER>/ET-BERT/output/datasets/train_dataset.tsv",
        "/home/<USER>/ET-BERT/output/datasets/valid_dataset.tsv",
        "/home/<USER>/ET-BERT/output/datasets/test_dataset.tsv"
    ]
    
    for tsv_file in tsv_files:
        if not os.path.exists(tsv_file):
            print(f"  ❌ 训练数据文件不存在: {tsv_file}")
            return False
    
    # 构建训练命令
    train_cmd = [
        sys.executable, "fine-tuning/run_classifier.py",
        "--pretrained_model_path", "models/pretrained_model.bin",
        "--vocab_path", "models/encryptd_vocab.txt",
        "--train_path", "/home/<USER>/ET-BERT/output/datasets/train_dataset.tsv",
        "--dev_path", "/home/<USER>/ET-BERT/output/datasets/valid_dataset.tsv",
        "--test_path", "/home/<USER>/ET-BERT/output/datasets/test_dataset.tsv",
        "--epochs_num", "200",
        "--batch_size", "32",
        "--embedding", "word_pos_seg",
        "--encoder", "transformer", 
        "--mask", "fully_visible",
        "--seq_length", "128",
        "--learning_rate", "2e-5",
        "--labels_num", str(num_classes)
    ]
    
    print("  🔄 执行训练命令...")
    print("  命令:", " ".join(train_cmd))
    
    try:
        result = subprocess.run(train_cmd, timeout=7200)  # 2小时超时
        
        if result.returncode == 0:
            print("  ✅ 模型训练完成")
            return True
        else:
            print("  ❌ 模型训练失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("  ⏰ 训练超时（超过2小时）")
        return False
    except Exception as e:
        print(f"  ❌ 训练出错: {e}")
        return False

def run_model_inference(num_classes):
    """运行模型推理"""
    print("🔮 开始模型推理...")
    
    # 检查模型文件是否存在
    model_file = "models/finetuned_model.bin"
    if not os.path.exists(model_file):
        print(f"  ❌ 微调模型不存在: {model_file}")
        return False
    
    # 构建推理命令
    infer_cmd = [
        sys.executable, "inference/run_classifier_infer.py",
        "--load_model_path", "models/finetuned_model.bin",
        "--vocab_path", "models/encryptd_vocab.txt", 
        "--test_path", "/home/<USER>/ET-BERT/output/datasets/nolabel_test_dataset.tsv",
        "--prediction_path", "/home/<USER>/ET-BERT/output/prediction.tsv",
        "--labels_num", str(num_classes),
        "--embedding", "word_pos_seg",
        "--encoder", "transformer",
        "--mask", "fully_visible",
        "--seq_length", "128",
        "--batch_size", "32"
    ]
    
    print("  🔄 执行推理命令...")
    
    try:
        result = subprocess.run(infer_cmd, timeout=1800)  # 30分钟超时
        
        if result.returncode == 0:
            print("  ✅ 模型推理完成")
            print(f"  📊 预测结果保存在: /home/<USER>/ET-BERT/output/prediction.tsv")
            return True
        else:
            print("  ❌ 模型推理失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("  ⏰ 推理超时（超过30分钟）")
        return False
    except Exception as e:
        print(f"  ❌ 推理出错: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='NetMamba数据集ET-BERT适配脚本')
    parser.add_argument('--samples_per_class', type=int, default=1000,
                       help='每个类别的样本数量 (默认: 1000)')
    parser.add_argument('--step', choices=['preprocess', 'train', 'inference', 'all'],
                       default='all', help='要执行的步骤 (默认: all)')
    parser.add_argument('--skip_check', action='store_true',
                       help='跳过数据集结构检查')
    parser.add_argument('--force_regenerate', action='store_true',
                       help='强制重新生成数据集（删除旧文件）')
    
    args = parser.parse_args()
    
    print("🚀 NetMamba数据集ET-BERT适配")
    print("=" * 50)
    
    # 检查数据集结构
    if not args.skip_check:
        valid, num_classes = check_dataset_structure()
        if not valid:
            print("❌ 数据集检查失败，请确认数据集路径和结构正确")
            return 1
    else:
        # 如果跳过检查，需要手动指定类别数
        print("⚠️  跳过数据集检查，请确保手动设置正确的类别数量")
        num_classes = 10  # 默认值，用户需要根据实际情况修改
    
    print(f"📊 检测到 {num_classes} 个类别")
    
    # 创建输出目录
    setup_output_directories()
    
    # 执行指定步骤
    success = True
    
    if args.step in ['preprocess', 'all']:
        # 根据参数决定是否强制重新生成数据集
        force_regen = args.force_regenerate or args.step == 'preprocess'  # 单独运行preprocess时默认重新生成
        success = run_data_preprocessing(num_classes, args.samples_per_class, force_regenerate=force_regen)
        if not success:
            print("❌ 数据预处理失败，停止执行")
            return 1
    
    if args.step in ['train', 'all'] and success:
        success = run_model_training(num_classes)
        if not success:
            print("❌ 模型训练失败，停止执行")
            return 1
    
    if args.step in ['inference', 'all'] and success:
        success = run_model_inference(num_classes)
        if not success:
            print("❌ 模型推理失败")
            return 1
    
    if success:
        print("\n🎉 所有步骤完成!")
        print("📁 输出文件位置:")
        print(f"  - 处理后的数据: /home/<USER>/ET-BERT/output/result/")
        print(f"  - 训练数据: /home/<USER>/ET-BERT/output/datasets/")
        print(f"  - 微调模型: models/finetuned_model.bin")
        print(f"  - 预测结果: /home/<USER>/ET-BERT/output/prediction.tsv")
        return 0
    else:
        print("❌ 执行过程中出现错误")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
