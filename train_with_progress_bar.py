#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
ET-BERT训练 - 连续进度条版本
模拟NetMamba风格的进度条显示
"""

import os
import sys
import subprocess
import time
import re
from tqdm import tqdm
import threading

class ETBERTProgressMonitor:
    def __init__(self):
        self.current_epoch = 0
        self.total_epochs = 100
        self.current_step = 0
        self.total_steps = 800  # 估计每个epoch的步数
        self.current_loss = 0.0
        self.current_acc = 0.0
        self.best_acc = 0.0
        self.epoch_pbar = None
        self.step_pbar = None
        self.start_time = time.time()
        
    def start_training_display(self):
        """开始训练显示"""
        print("🚀 ET-BERT Training with Progress Bars")
        print("=" * 80)
        
        # 创建epoch级别的进度条
        self.epoch_pbar = tqdm(
            total=self.total_epochs,
            desc="[ET-BERT] Overall Progress",
            position=0,
            leave=True,
            bar_format='{desc}: {percentage:3.0f}%|{bar}| {n}/{total} [{elapsed}<{remaining}] Best Acc: {postfix[best_acc]:.4f}',
            postfix={'best_acc': 0.0000}
        )
        
    def update_epoch(self, epoch):
        """更新epoch"""
        if epoch != self.current_epoch:
            # 完成上一个epoch的进度条
            if self.step_pbar:
                self.step_pbar.close()
            
            self.current_epoch = epoch
            self.current_step = 0
            
            # 更新epoch进度条
            if self.epoch_pbar:
                self.epoch_pbar.n = epoch
                self.epoch_pbar.set_postfix({'best_acc': self.best_acc})
                self.epoch_pbar.refresh()
            
            # 创建新的step进度条
            self.step_pbar = tqdm(
                total=self.total_steps,
                desc=f"Epoch {epoch}",
                position=1,
                leave=False,
                bar_format='{desc}: {percentage:3.0f}%|{bar}| {n}/{total} [{elapsed}<{remaining}, loss={postfix[loss]:.6f}, lr={postfix[lr]:.6f}]',
                postfix={'loss': 0.0, 'lr': 0.00002}
            )
    
    def update_step(self, step, loss=None):
        """更新步骤"""
        self.current_step = step
        if loss is not None:
            self.current_loss = loss
        
        if self.step_pbar:
            self.step_pbar.n = step
            self.step_pbar.set_postfix({
                'loss': self.current_loss,
                'lr': 0.002  # 固定学习率显示
            })
            self.step_pbar.refresh()
    
    def update_epoch_results(self, loss, acc, f1=None, precision=None, recall=None):
        """更新epoch结果"""
        self.current_loss = loss
        self.current_acc = acc
        
        if acc > self.best_acc:
            self.best_acc = acc
            best_indicator = " (Best model saved)"
        else:
            best_indicator = ""
        
        # 完成当前epoch的step进度条
        if self.step_pbar:
            self.step_pbar.n = self.total_steps
            self.step_pbar.refresh()
            self.step_pbar.close()
        
        # 显示epoch结果
        if f1 and precision and recall:
            result_str = f"Epoch {self.current_epoch}: Loss={loss:.4f} | Valid Acc={acc:.4f} | F1={f1:.4f} | Precision={precision:.4f} | Recall={recall:.4f}{best_indicator}"
        else:
            result_str = f"Epoch {self.current_epoch}: Loss={loss:.4f} | Valid Acc={acc:.4f}{best_indicator}"
        
        tqdm.write(result_str)
        
        # 更新总体进度条
        if self.epoch_pbar:
            self.epoch_pbar.set_postfix({'best_acc': self.best_acc})
    
    def close(self):
        """关闭进度条"""
        if self.step_pbar:
            self.step_pbar.close()
        if self.epoch_pbar:
            self.epoch_pbar.close()

def run_training_with_progress():
    """运行带进度条的训练"""
    
    # 检查必要文件
    required_files = [
        "/home/<USER>/ET-BERT/output/datasets/train_dataset.tsv",
        "/home/<USER>/ET-BERT/output/datasets/valid_dataset.tsv",
        "models/pretrained_model.bin"
    ]
    
    print("🔍 检查必要文件...")
    for file_path in required_files:
        if os.path.exists(file_path):
            size_mb = os.path.getsize(file_path) / (1024*1024)
            print(f"  ✅ {os.path.basename(file_path)}: {size_mb:.2f} MB")
        else:
            print(f"  ❌ {file_path}: 不存在")
            return False
    
    # 构建训练命令
    train_cmd = [
        sys.executable, "fine-tuning/run_classifier.py",
        "--pretrained_model_path", "models/pretrained_model.bin",
        "--vocab_path", "models/encryptd_vocab.txt",
        "--train_path", "/home/<USER>/ET-BERT/output/datasets/train_dataset.tsv",
        "--dev_path", "/home/<USER>/ET-BERT/output/datasets/valid_dataset.tsv",
        "--test_path", "/home/<USER>/ET-BERT/output/datasets/test_dataset.tsv",
        "--epochs_num", "100",
        "--batch_size", "16",
        "--embedding", "word_pos_seg",
        "--encoder", "transformer",
        "--mask", "fully_visible",
        "--seq_length", "128",
        "--learning_rate", "2e-3",
        "--output_model_path", "models/finetuned_model.bin"
    ]
    
    print("\n📝 训练命令:")
    print("  " + " ".join(train_cmd))
    
    # 创建进度监控器
    monitor = ETBERTProgressMonitor()
    monitor.start_training_display()
    
    try:
        # 启动训练进程
        process = subprocess.Popen(
            train_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=0
        )
        
        # 解析输出并更新进度条
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            
            if output:
                line = output.strip()
                
                # 跳过警告信息
                skip_patterns = [
                    "Was asked to gather along dimension 0",
                    "warnings.warn",
                    "UserWarning"
                ]
                if any(pattern in line for pattern in skip_patterns):
                    continue
                
                # 解析epoch信息
                epoch_match = re.search(r'Epoch id:\s*(\d+)', line)
                if epoch_match:
                    epoch = int(epoch_match.group(1))
                    monitor.update_epoch(epoch)
                
                # 解析训练步骤和损失
                step_loss_match = re.search(r'Training steps:\s*(\d+).*Avg loss:\s*([\d.]+)', line)
                if step_loss_match:
                    step = int(step_loss_match.group(1))
                    loss = float(step_loss_match.group(2))
                    # 将step映射到当前epoch内的进度
                    epoch_step = step % monitor.total_steps
                    if epoch_step == 0:
                        epoch_step = monitor.total_steps
                    monitor.update_step(epoch_step, loss)
                
                # 解析epoch结束时的准确率
                acc_match = re.search(r'Acc\.\s*\(Correct/Total\):\s*([\d.]+)', line)
                if acc_match:
                    acc = float(acc_match.group(1))
                    monitor.update_epoch_results(monitor.current_loss, acc)
                
                # 检查错误
                if any(keyword in line.lower() for keyword in ['error', 'exception', 'failed']):
                    tqdm.write(f"❌ {line}")
                
                # 检查重要信息
                if any(keyword in line.lower() for keyword in ['start training', 'best result', 'complete']):
                    tqdm.write(f"🎯 {line}")
        
        # 获取返回码
        return_code = process.poll()
        
        # 关闭进度条
        monitor.close()
        
        # 显示结果
        total_time = (time.time() - monitor.start_time) / 60
        print(f"\n📊 训练完成!")
        print(f"  返回码: {return_code}")
        print(f"  运行时间: {total_time:.1f} 分钟")
        print(f"  最佳准确率: {monitor.best_acc:.4f}")
        
        if return_code == 0:
            print("✅ 训练成功!")
            
            # 检查模型文件
            model_path = "models/finetuned_model.bin"
            if os.path.exists(model_path):
                model_size = os.path.getsize(model_path) / (1024*1024)
                print(f"📦 模型已保存: {model_size:.2f} MB")
            
            return True
        else:
            print("❌ 训练失败")
            return False
            
    except KeyboardInterrupt:
        monitor.close()
        print(f"\n⚠️  训练被用户中断")
        process.terminate()
        return False
    except Exception as e:
        monitor.close()
        print(f"❌ 训练出错: {e}")
        return False

def main():
    print("🚀 ET-BERT 连续进度条训练")
    print("=" * 60)
    
    if run_training_with_progress():
        print("\n🎉 训练完成! 现在可以运行推理:")
        print("  python run_inference_only.py")
        return 0
    else:
        print("\n❌ 训练失败")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 用户中断程序")
        sys.exit(1)
