#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
安装ET-BERT所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装: {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {package} - {e}")
        return False

def install_dependencies():
    """安装所有依赖包"""
    print("🔧 开始安装ET-BERT依赖包...")
    
    # 基础依赖包
    basic_packages = [
        "torch>=1.0",
        "six>=1.12.0", 
        "packaging",
        "xlrd>=1.2.0",
        "numpy>=1.19.2",
        "scikit-learn",
        "pandas",
        "tqdm"
    ]
    
    # 网络分析相关包 - 使用兼容的版本
    network_packages = [
        "scapy>=2.4.5"  # 使用更新的版本以兼容Python 3.12
    ]
    
    # 可选包（如果安装失败不影响主要功能）
    optional_packages = [
        "flowcontainer"
    ]
    
    failed_packages = []
    
    # 安装基础包
    print("\n📦 安装基础依赖包...")
    for package in basic_packages:
        if not install_package(package):
            failed_packages.append(package)
    
    # 安装网络分析包
    print("\n🌐 安装网络分析包...")
    for package in network_packages:
        if not install_package(package):
            failed_packages.append(package)
    
    # 安装可选包
    print("\n🔧 安装可选包...")
    for package in optional_packages:
        if not install_package(package):
            print(f"⚠️  可选包安装失败: {package} (不影响主要功能)")
    
    # 总结
    print("\n" + "="*50)
    if failed_packages:
        print("⚠️  以下包安装失败:")
        for package in failed_packages:
            print(f"  - {package}")
        print("\n请手动安装这些包或检查网络连接")
        return False
    else:
        print("🎉 所有依赖包安装完成!")
        return True

def check_imports():
    """检查关键模块是否可以导入"""
    print("\n🔍 检查模块导入...")
    
    modules_to_check = [
        ("torch", "PyTorch"),
        ("numpy", "NumPy"),
        ("pandas", "Pandas"),
        ("sklearn", "Scikit-learn"),
        ("scapy", "Scapy"),
        ("xlrd", "XLRD"),
        ("tqdm", "TQDM")
    ]
    
    failed_imports = []
    
    for module, name in modules_to_check:
        try:
            __import__(module)
            print(f"  ✅ {name}")
        except ImportError:
            print(f"  ❌ {name}")
            failed_imports.append(name)
    
    if failed_imports:
        print(f"\n⚠️  以下模块导入失败: {', '.join(failed_imports)}")
        return False
    else:
        print("\n✅ 所有关键模块导入成功!")
        return True

def create_simple_flowcontainer():
    """创建一个简化的flowcontainer模块替代"""
    print("\n🔧 创建flowcontainer替代模块...")
    
    flowcontainer_code = '''
"""
简化的flowcontainer模块替代
用于ET-BERT项目
"""

def extract(pcap_file, filter='tcp', extension=None):
    """
    简化的extract函数
    返回空字典，避免导入错误
    """
    print(f"Warning: Using simplified flowcontainer for {pcap_file}")
    return {}

class FlowContainer:
    def __init__(self):
        self.ip_lengths = []
        self.ip_timestamps = []
        self.extension = {}
'''
    
    # 创建flowcontainer目录和文件
    try:
        os.makedirs("flowcontainer", exist_ok=True)
        
        with open("flowcontainer/__init__.py", "w") as f:
            f.write('from .extractor import extract\n')
        
        with open("flowcontainer/extractor.py", "w") as f:
            f.write(flowcontainer_code)
        
        print("  ✅ 创建flowcontainer替代模块成功")
        return True
    except Exception as e:
        print(f"  ❌ 创建flowcontainer替代模块失败: {e}")
        return False

def main():
    print("🚀 ET-BERT依赖安装脚本")
    print("="*50)
    
    # 安装依赖包
    install_success = install_dependencies()
    
    # 检查导入
    import_success = check_imports()
    
    # 如果flowcontainer导入失败，创建替代模块
    try:
        import flowcontainer
        print("✅ flowcontainer 模块可用")
    except ImportError:
        print("⚠️  flowcontainer 模块不可用，创建替代模块...")
        create_simple_flowcontainer()
    
    print("\n" + "="*50)
    if install_success and import_success:
        print("🎉 环境配置完成! 现在可以运行ET-BERT了。")
        print("\n下一步:")
        print("  python run_netmamba_dataset.py --step all")
    else:
        print("⚠️  环境配置存在问题，请检查上述错误信息。")
        print("\n手动安装命令:")
        print("  pip install torch numpy pandas scikit-learn scapy==2.4.4 xlrd tqdm")
    
    return 0 if (install_success and import_success) else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
