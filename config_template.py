#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
ET-BERT 自定义数据集配置模板
请根据您的实际情况修改以下配置参数
"""

# ==================== 基本路径配置 ====================

# 您的PCAP文件输入路径 (必须修改)
# 支持两种组织方式:
# 1. 按类别分文件夹: /path/to/data/class1/, /path/to/data/class2/, ...
# 2. 所有文件在一个文件夹: /path/to/data/ (脚本会自动创建class_0文件夹)
PCAP_INPUT_PATH = "D:/your_pcap_data/"

# 输出路径 (必须修改)
OUTPUT_PATH = "D:/et_bert_output/"

# ==================== 数据集参数 ====================

# 数据集类别数量 (必须修改)
NUM_CLASSES = 5

# 每个类别的样本数量
SAMPLES_PER_CLASS = 1000

# 数据集级别: 'packet' 或 'flow'
# - packet: 包级别分析，每个数据包作为一个样本
# - flow: 流级别分析，每个网络流作为一个样本
DATASET_LEVEL = 'packet'

# ==================== 特征提取参数 ====================

# 载荷长度 (字节数的一半，因为是十六进制表示)
PAYLOAD_LENGTH = 128

# 要提取的特征类型
FEATURES = ['payload']  # 可选: ['payload', 'length', 'time', 'direction', 'message_type']

# ==================== 文件过滤参数 ====================

# 最小文件大小 (KB) - 小于此大小的文件将被删除
MIN_FILE_SIZE_KB = 1.0

# ==================== 工具路径配置 ====================

# SplitCap工具路径 (如果不在系统PATH中，请指定完整路径)
SPLITCAP_PATH = "SplitCap.exe"

# tshark工具路径 (如果不在系统PATH中，请指定完整路径)
TSHARK_PATH = "tshark.exe"

# ==================== 高级参数 ====================

# 随机种子 (用于数据集分割的可重现性)
RANDOM_SEED = 42

# 数据集分割比例 [训练集, 验证集, 测试集]
DATASET_SPLIT_RATIO = [0.7, 0.15, 0.15]

# 是否清理中间文件
CLEAN_INTERMEDIATE_FILES = False

# ==================== 预训练相关参数 ====================

# 词汇表路径
VOCAB_PATH = "models/encryptd_vocab.txt"

# 预训练模型路径
PRETRAINED_MODEL_PATH = "models/pretrained_model.bin"

# ==================== 训练参数 ====================

# 批次大小
BATCH_SIZE = 32

# 训练轮数
EPOCHS = 10

# 学习率
LEARNING_RATE = 2e-5

# 序列长度
SEQUENCE_LENGTH = 128

# ==================== 示例配置 ====================

# 示例1: 恶意软件流量分类
EXAMPLE_MALWARE_CONFIG = {
    'pcap_input_path': 'D:/malware_pcaps/',
    'output_path': 'D:/malware_output/',
    'num_classes': 10,  # 10种恶意软件家族
    'samples_per_class': 500,
    'dataset_level': 'flow',
    'payload_length': 256,
    'features': ['payload', 'length', 'direction']
}

# 示例2: 应用程序流量分类
EXAMPLE_APP_CONFIG = {
    'pcap_input_path': 'D:/app_traffic/',
    'output_path': 'D:/app_output/',
    'num_classes': 20,  # 20种应用程序
    'samples_per_class': 1000,
    'dataset_level': 'packet',
    'payload_length': 128,
    'features': ['payload']
}

# 示例3: 网络协议分类
EXAMPLE_PROTOCOL_CONFIG = {
    'pcap_input_path': 'D:/protocol_pcaps/',
    'output_path': 'D:/protocol_output/',
    'num_classes': 5,  # 5种协议类型
    'samples_per_class': 2000,
    'dataset_level': 'packet',
    'payload_length': 64,
    'features': ['payload', 'length']
}

def get_config():
    """
    返回当前配置
    您可以修改这个函数来返回您想要的配置
    """
    config = {
        'pcap_input_path': PCAP_INPUT_PATH,
        'output_path': OUTPUT_PATH,
        'num_classes': NUM_CLASSES,
        'samples_per_class': SAMPLES_PER_CLASS,
        'dataset_level': DATASET_LEVEL,
        'payload_length': PAYLOAD_LENGTH,
        'features': FEATURES,
        'min_file_size_kb': MIN_FILE_SIZE_KB,
        'random_seed': RANDOM_SEED,
        'dataset_split_ratio': DATASET_SPLIT_RATIO,
        'clean_intermediate_files': CLEAN_INTERMEDIATE_FILES,
        'vocab_path': VOCAB_PATH,
        'pretrained_model_path': PRETRAINED_MODEL_PATH,
        'batch_size': BATCH_SIZE,
        'epochs': EPOCHS,
        'learning_rate': LEARNING_RATE,
        'sequence_length': SEQUENCE_LENGTH,
        'splitcap_path': SPLITCAP_PATH,
        'tshark_path': TSHARK_PATH
    }
    
    return config

def validate_config(config):
    """
    验证配置参数的有效性
    """
    errors = []
    
    # 检查必需的路径
    if not config.get('pcap_input_path'):
        errors.append("pcap_input_path 不能为空")
    
    if not config.get('output_path'):
        errors.append("output_path 不能为空")
    
    # 检查数值参数
    if config.get('num_classes', 0) <= 0:
        errors.append("num_classes 必须大于0")
    
    if config.get('samples_per_class', 0) <= 0:
        errors.append("samples_per_class 必须大于0")
    
    # 检查数据集级别
    if config.get('dataset_level') not in ['packet', 'flow']:
        errors.append("dataset_level 必须是 'packet' 或 'flow'")
    
    # 检查分割比例
    split_ratio = config.get('dataset_split_ratio', [])
    if len(split_ratio) != 3 or abs(sum(split_ratio) - 1.0) > 0.01:
        errors.append("dataset_split_ratio 必须是三个数字且和为1.0")
    
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    return True

def print_config(config):
    """
    打印当前配置
    """
    print("当前配置:")
    print("=" * 50)
    for key, value in config.items():
        print(f"{key:25}: {value}")
    print("=" * 50)

if __name__ == "__main__":
    # 获取并验证配置
    config = get_config()
    
    if validate_config(config):
        print_config(config)
        print("\n配置验证通过!")
    else:
        print("\n请修正配置错误后重试。")
