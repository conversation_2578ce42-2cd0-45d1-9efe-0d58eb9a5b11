#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
跳过数据预处理，直接进行模型训练和推理
显示详细的训练指标：损失值、准确率、F1、精确率、召回率
"""

import os
import sys
import subprocess
import time
import re
from pathlib import Path

def check_preprocessed_data():
    """检查预处理数据是否存在"""
    print("🔍 检查预处理数据...")
    
    required_files = [
        "/home/<USER>/ET-BERT/output/result/dataset/x_datagram_train.npy",
        "/home/<USER>/ET-BERT/output/result/dataset/x_datagram_test.npy", 
        "/home/<USER>/ET-BERT/output/result/dataset/x_datagram_valid.npy",
        "/home/<USER>/ET-BERT/output/result/dataset/y_train.npy",
        "/home/<USER>/ET-BERT/output/result/dataset/y_test.npy",
        "/home/<USER>/ET-BERT/output/result/dataset/y_valid.npy"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path) / (1024*1024)  # MB
            print(f"  ✅ {os.path.basename(file_path)}: {file_size:.2f} MB")
        else:
            print(f"  ❌ {os.path.basename(file_path)}: 不存在")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少 {len(missing_files)} 个预处理文件")
        return False
    else:
        print("✅ 所有预处理文件都存在")
        return True

def check_model_files():
    """检查模型文件"""
    print("\n🔍 检查模型文件...")
    
    model_files = {
        "预训练模型": "models/pretrained_model.bin",
        "词汇表": "models/encryptd_vocab.txt", 
        "配置文件": "models/bert_base_config.json"
    }
    
    missing_files = []
    for name, file_path in model_files.items():
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path) / (1024*1024)  # MB
            print(f"  ✅ {name}: {file_size:.2f} MB")
        else:
            print(f"  ❌ {name}: 不存在 ({file_path})")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def run_model_training():
    """运行模型训练并显示详细指标"""
    print("\n🤖 开始模型训练...")
    
    # 构建训练命令
    train_cmd = [
        sys.executable, "fine-tuning/run_classifier.py",
        "--pretrained_model_path", "models/pretrained_model.bin",
        "--vocab_path", "models/encryptd_vocab.txt",
        "--train_path", "/home/<USER>/ET-BERT/output/datasets/train_dataset.tsv",
        "--dev_path", "/home/<USER>/ET-BERT/output/datasets/valid_dataset.tsv",
        "--test_path", "/home/<USER>/ET-BERT/output/datasets/test_dataset.tsv",
        "--epochs_num", "10",
        "--batch_size", "32",
        "--embedding", "word_pos_seg",
        "--encoder", "transformer",
        "--mask", "fully_visible", 
        "--seq_length", "128",
        "--learning_rate", "2e-5",
        "--labels_num", "18",
        "--output_model_path", "models/finetuned_model.bin"
    ]
    
    print("📝 训练命令:")
    print("  " + " ".join(train_cmd))
    print()
    
    try:
        # 启动训练进程
        process = subprocess.Popen(
            train_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print("📊 训练进度监控:")
        print("=" * 80)
        
        # 用于存储指标
        epoch_metrics = []
        current_epoch = 0
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            
            if output:
                line = output.strip()
                print(f"  {line}")
                
                # 解析训练指标
                metrics = parse_training_metrics(line)
                if metrics:
                    current_epoch = metrics.get('epoch', current_epoch)
                    epoch_metrics.append(metrics)
                    display_metrics(metrics)
        
        return_code = process.poll()
        
        if return_code == 0:
            print("\n✅ 模型训练完成!")
            
            # 显示训练总结
            if epoch_metrics:
                display_training_summary(epoch_metrics)
            
            return True
        else:
            print(f"\n❌ 模型训练失败，返回码: {return_code}")
            return False
            
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        return False

def parse_training_metrics(line):
    """解析训练输出中的指标"""
    metrics = {}
    
    # 解析epoch信息
    epoch_match = re.search(r'Epoch\s*:?\s*(\d+)', line, re.IGNORECASE)
    if epoch_match:
        metrics['epoch'] = int(epoch_match.group(1))
    
    # 解析损失值
    loss_patterns = [
        r'loss\s*:?\s*([\d.]+)',
        r'Loss\s*:?\s*([\d.]+)',
        r'training_loss\s*:?\s*([\d.]+)'
    ]
    for pattern in loss_patterns:
        loss_match = re.search(pattern, line, re.IGNORECASE)
        if loss_match:
            metrics['loss'] = float(loss_match.group(1))
            break
    
    # 解析准确率
    acc_patterns = [
        r'accuracy\s*:?\s*([\d.]+)',
        r'acc\s*:?\s*([\d.]+)',
        r'Accuracy\s*:?\s*([\d.]+)'
    ]
    for pattern in acc_patterns:
        acc_match = re.search(pattern, line, re.IGNORECASE)
        if acc_match:
            metrics['accuracy'] = float(acc_match.group(1))
            break
    
    # 解析F1分数
    f1_patterns = [
        r'f1\s*:?\s*([\d.]+)',
        r'F1\s*:?\s*([\d.]+)',
        r'f1[-_]score\s*:?\s*([\d.]+)'
    ]
    for pattern in f1_patterns:
        f1_match = re.search(pattern, line, re.IGNORECASE)
        if f1_match:
            metrics['f1'] = float(f1_match.group(1))
            break
    
    # 解析精确率
    precision_patterns = [
        r'precision\s*:?\s*([\d.]+)',
        r'Precision\s*:?\s*([\d.]+)'
    ]
    for pattern in precision_patterns:
        prec_match = re.search(pattern, line, re.IGNORECASE)
        if prec_match:
            metrics['precision'] = float(prec_match.group(1))
            break
    
    # 解析召回率
    recall_patterns = [
        r'recall\s*:?\s*([\d.]+)',
        r'Recall\s*:?\s*([\d.]+)'
    ]
    for pattern in recall_patterns:
        recall_match = re.search(pattern, line, re.IGNORECASE)
        if recall_match:
            metrics['recall'] = float(recall_match.group(1))
            break
    
    return metrics if metrics else None

def display_metrics(metrics):
    """显示当前指标"""
    if not metrics:
        return
    
    metric_str = []
    if 'epoch' in metrics:
        metric_str.append(f"Epoch: {metrics['epoch']}")
    if 'loss' in metrics:
        metric_str.append(f"Loss: {metrics['loss']:.4f}")
    if 'accuracy' in metrics:
        metric_str.append(f"Acc: {metrics['accuracy']:.4f}")
    if 'f1' in metrics:
        metric_str.append(f"F1: {metrics['f1']:.4f}")
    if 'precision' in metrics:
        metric_str.append(f"Prec: {metrics['precision']:.4f}")
    if 'recall' in metrics:
        metric_str.append(f"Recall: {metrics['recall']:.4f}")
    
    if metric_str:
        print(f"    📊 {' | '.join(metric_str)}")

def display_training_summary(epoch_metrics):
    """显示训练总结"""
    print("\n📈 训练总结:")
    print("=" * 80)
    
    # 按epoch分组
    epochs = {}
    for metrics in epoch_metrics:
        epoch = metrics.get('epoch', 0)
        if epoch not in epochs:
            epochs[epoch] = []
        epochs[epoch].append(metrics)
    
    # 显示每个epoch的最佳指标
    print(f"{'Epoch':<8} {'Loss':<10} {'Accuracy':<10} {'F1':<10} {'Precision':<10} {'Recall':<10}")
    print("-" * 80)
    
    for epoch in sorted(epochs.keys()):
        epoch_data = epochs[epoch]
        
        # 取该epoch的最后一个指标（通常是最终结果）
        final_metrics = epoch_data[-1]
        
        loss = final_metrics.get('loss', 0)
        acc = final_metrics.get('accuracy', 0)
        f1 = final_metrics.get('f1', 0)
        prec = final_metrics.get('precision', 0)
        recall = final_metrics.get('recall', 0)
        
        print(f"{epoch:<8} {loss:<10.4f} {acc:<10.4f} {f1:<10.4f} {prec:<10.4f} {recall:<10.4f}")

def run_model_inference():
    """运行模型推理"""
    print("\n🔮 开始模型推理...")
    
    # 检查微调模型是否存在
    finetuned_model = "models/finetuned_model.bin"
    if not os.path.exists(finetuned_model):
        print(f"❌ 微调模型不存在: {finetuned_model}")
        return False
    
    # 构建推理命令
    infer_cmd = [
        sys.executable, "inference/run_classifier_infer.py",
        "--load_model_path", "models/finetuned_model.bin",
        "--vocab_path", "models/encryptd_vocab.txt",
        "--test_path", "/home/<USER>/ET-BERT/output/datasets/nolabel_test_dataset.tsv",
        "--prediction_path", "/home/<USER>/ET-BERT/output/prediction.tsv",
        "--labels_num", "18",
        "--embedding", "word_pos_seg",
        "--encoder", "transformer",
        "--mask", "fully_visible",
        "--seq_length", "128",
        "--batch_size", "32"
    ]
    
    print("📝 推理命令:")
    print("  " + " ".join(infer_cmd))
    print()
    
    try:
        # 启动推理进程
        process = subprocess.Popen(
            infer_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print("📊 推理进度:")
        print("-" * 50)
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            
            if output:
                line = output.strip()
                print(f"  {line}")
        
        return_code = process.poll()
        
        if return_code == 0:
            print("\n✅ 模型推理完成!")
            
            # 检查预测文件是否生成
            prediction_file = "/home/<USER>/ET-BERT/output/prediction.tsv"
            if os.path.exists(prediction_file):
                file_size = os.path.getsize(prediction_file)
                print(f"📊 预测文件已生成: {prediction_file} ({file_size} bytes)")
                
                # 显示前几行预测结果
                print("\n📋 预测结果预览:")
                try:
                    with open(prediction_file, 'r') as f:
                        for i, line in enumerate(f):
                            if i < 5:
                                print(f"  {line.strip()}")
                            else:
                                break
                except:
                    pass
                
                return True
            else:
                print(f"❌ 预测文件未生成: {prediction_file}")
                return False
        else:
            print(f"\n❌ 模型推理失败，返回码: {return_code}")
            return False
            
    except Exception as e:
        print(f"❌ 推理过程出错: {e}")
        return False

def main():
    print("🚀 ET-BERT 训练和推理流程")
    print("=" * 60)
    
    # 检查预处理数据
    if not check_preprocessed_data():
        print("❌ 预处理数据不完整，请先运行数据预处理")
        return 1
    
    # 检查模型文件
    if not check_model_files():
        print("❌ 模型文件不完整，请检查模型文件")
        return 1
    
    # 运行训练
    if not run_model_training():
        print("❌ 训练失败")
        return 1
    
    # 运行推理
    if not run_model_inference():
        print("❌ 推理失败")
        return 1
    
    print("\n🎉 训练和推理完成!")
    print("现在可以运行 python analyze_results.py 来查看详细的评估结果")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
