#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
增强版训练脚本 - 提供详细的实时输出
"""

import os
import sys
import subprocess
import time
import threading
import signal

class TrainingMonitor:
    def __init__(self):
        self.start_time = time.time()
        self.line_count = 0
        self.last_heartbeat = time.time()
        self.running = True
        
    def heartbeat_monitor(self):
        """心跳监控线程"""
        while self.running:
            time.sleep(30)  # 每30秒检查一次
            if self.running:
                elapsed = time.time() - self.start_time
                print(f"\n💓 [心跳] 程序运行中... 已运行 {elapsed/60:.1f} 分钟，输出了 {self.line_count} 行")
                print(f"    当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"    如果长时间无输出，可以按 Ctrl+C 中断\n")
    
    def start_heartbeat(self):
        """启动心跳监控"""
        heartbeat_thread = threading.Thread(target=self.heartbeat_monitor, daemon=True)
        heartbeat_thread.start()
    
    def stop(self):
        """停止监控"""
        self.running = False

def run_training_with_verbose_output():
    """运行训练并提供详细输出"""
    print("🚀 启动增强版训练监控")
    print("=" * 80)
    
    # 检查必要文件
    required_files = [
        "/home/<USER>/ET-BERT/output/datasets/train_dataset.tsv",
        "/home/<USER>/ET-BERT/output/datasets/valid_dataset.tsv",
        "/home/<USER>/ET-BERT/output/datasets/test_dataset.tsv",
        "models/pretrained_model.bin",
        "models/encryptd_vocab.txt"
    ]
    
    print("🔍 检查必要文件...")
    for file_path in required_files:
        if os.path.exists(file_path):
            size_mb = os.path.getsize(file_path) / (1024*1024)
            print(f"  ✅ {os.path.basename(file_path)}: {size_mb:.2f} MB")
        else:
            print(f"  ❌ {file_path}: 不存在")
            return False
    
    # 构建训练命令
    train_cmd = [
        sys.executable, "fine-tuning/run_classifier.py",
        "--pretrained_model_path", "models/pretrained_model.bin",
        "--vocab_path", "models/encryptd_vocab.txt",
        "--train_path", "/home/<USER>/ET-BERT/output/datasets/train_dataset.tsv",
        "--dev_path", "/home/<USER>/ET-BERT/output/datasets/valid_dataset.tsv",
        "--test_path", "/home/<USER>/ET-BERT/output/datasets/test_dataset.tsv",
        "--epochs_num", "10",
        "--batch_size", "16",  # 减少批次大小以避免内存问题
        "--embedding", "word_pos_seg",
        "--encoder", "transformer",
        "--mask", "fully_visible",
        "--seq_length", "128",
        "--learning_rate", "2e-5",
        "--output_model_path", "models/finetuned_model.bin"
    ]
    
    print("\n📝 训练命令:")
    print("  " + " ".join(train_cmd))
    print()
    
    # 创建监控器
    monitor = TrainingMonitor()
    
    try:
        # 启动心跳监控
        monitor.start_heartbeat()
        
        print("🔄 启动训练进程...")
        print("💡 提示: 如果长时间无输出，程序可能在处理大量数据，请耐心等待")
        print("⚠️  如需中断，请按 Ctrl+C")
        print("=" * 80)
        
        # 启动训练进程
        process = subprocess.Popen(
            train_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=0,  # 无缓冲
            preexec_fn=os.setsid if hasattr(os, 'setsid') else None
        )
        
        # 实时输出处理
        while True:
            # 设置超时读取
            try:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                
                if output:
                    line = output.strip()
                    monitor.line_count += 1

                    # 过滤掉PyTorch的常见警告
                    skip_warnings = [
                        "Was asked to gather along dimension 0",
                        "warnings.warn('Was asked to gather",
                        "_functions.py:68: UserWarning"
                    ]

                    # 如果是需要跳过的警告，只计数不显示
                    if any(warning in line for warning in skip_warnings):
                        if monitor.line_count % 50 == 0:  # 每50个警告显示一次汇总
                            print(f"    ⚠️  已过滤 PyTorch 内部警告 (第{monitor.line_count}行)")
                        continue

                    # 添加时间戳和行号
                    timestamp = time.strftime("%H:%M:%S")
                    elapsed = time.time() - monitor.start_time

                    print(f"[{timestamp}] [{elapsed/60:6.1f}min] [{monitor.line_count:4d}] {line}")

                    # 检查特殊关键词 (排除已知的无害警告)
                    important_keywords = ['epoch', 'loss', 'accuracy', 'f1', 'precision', 'recall']
                    error_keywords = ['error', 'failed', 'exception']

                    if any(keyword in line.lower() for keyword in important_keywords):
                        print(f"    🎯 重要信息: {line}")
                    elif any(keyword in line.lower() for keyword in error_keywords):
                        print(f"    ❌ 错误信息: {line}")

                    # 检查进度条更新
                    if 'it/s' in line or '%|' in line:
                        print(f"    📊 进度更新: {line}")

                    # 强制刷新输出
                    sys.stdout.flush()
                    monitor.last_heartbeat = time.time()
                
                else:
                    # 如果没有输出，短暂等待
                    time.sleep(0.1)
                    
            except KeyboardInterrupt:
                print(f"\n⚠️  收到中断信号，正在终止训练进程...")
                process.terminate()
                time.sleep(2)
                if process.poll() is None:
                    print("强制终止进程...")
                    process.kill()
                break
        
        # 获取返回码
        return_code = process.poll()
        
        print(f"\n📊 训练完成!")
        print(f"  返回码: {return_code}")
        print(f"  总运行时间: {(time.time() - monitor.start_time)/60:.1f} 分钟")
        print(f"  总输出行数: {monitor.line_count}")
        
        if return_code == 0:
            print("✅ 训练成功完成!")
            
            # 检查输出模型
            model_path = "models/finetuned_model.bin"
            if os.path.exists(model_path):
                model_size = os.path.getsize(model_path) / (1024*1024)
                print(f"📦 微调模型已生成: {model_size:.2f} MB")
            
            return True
        else:
            print(f"❌ 训练失败，返回码: {return_code}")
            return False
            
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        return False
    finally:
        monitor.stop()

def show_system_info():
    """显示系统信息"""
    print("💻 系统信息:")
    
    try:
        import psutil
        memory = psutil.virtual_memory()
        print(f"  内存: {memory.percent:.1f}% 使用 ({memory.available/1024**3:.1f}GB 可用)")
        
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"  CPU: {cpu_percent:.1f}% 使用")
        
    except ImportError:
        print("  (需要安装 psutil 来显示详细系统信息)")
    
    try:
        import torch
        if torch.cuda.is_available():
            print(f"  GPU: {torch.cuda.device_count()} 个可用")
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                print(f"    GPU {i}: {gpu_name}")
        else:
            print("  GPU: 不可用，使用CPU训练")
    except ImportError:
        print("  (需要安装 torch 来检查GPU信息)")

def main():
    print("🚀 ET-BERT 增强版训练监控")
    print("=" * 60)
    
    # 显示系统信息
    show_system_info()
    print()
    
    # 运行训练
    success = run_training_with_verbose_output()
    
    if success:
        print("\n🎉 训练完成! 现在可以运行推理:")
        print("  python inference/run_classifier_infer.py [参数...]")
        return 0
    else:
        print("\n❌ 训练失败")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 用户中断程序")
        sys.exit(1)
