#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
增强版训练脚本 - 提供详细的实时输出
"""

import os
import sys
import subprocess
import time
import threading
import signal
import re
from tqdm import tqdm

class TrainingMonitor:
    def __init__(self):
        self.start_time = time.time()
        self.line_count = 0
        self.last_heartbeat = time.time()
        self.running = True
        self.current_epoch = 0
        self.total_epochs = 100
        self.current_step = 0
        self.total_steps = 0
        self.current_loss = 0.0
        self.current_acc = 0.0
        self.epoch_pbar = None
        self.step_pbar = None

    def create_progress_bars(self):
        """创建进度条"""
        # 主epoch进度条
        self.epoch_pbar = tqdm(
            total=self.total_epochs,
            desc="[ET-BERT] Training",
            position=0,
            leave=True,
            bar_format='{desc}: {percentage:3.0f}%|{bar}| {n}/{total} [{elapsed}<{remaining}, {rate_fmt}]'
        )

        # 当前epoch内的step进度条
        self.step_pbar = tqdm(
            total=100,  # 初始值，会动态更新
            desc=f"Epoch {self.current_epoch}",
            position=1,
            leave=False,
            bar_format='{desc}: {percentage:3.0f}%|{bar}| {n}/{total} [{elapsed}<{remaining}, loss={postfix[loss]:.6f}, acc={postfix[acc]:.4f}]',
            postfix={'loss': 0.0, 'acc': 0.0}
        )

    def update_epoch_progress(self, epoch, total_epochs=None):
        """更新epoch进度"""
        if total_epochs:
            self.total_epochs = total_epochs
            if self.epoch_pbar:
                self.epoch_pbar.total = total_epochs

        if epoch > self.current_epoch:
            self.current_epoch = epoch
            if self.epoch_pbar:
                self.epoch_pbar.n = epoch
                self.epoch_pbar.refresh()

            # 重置step进度条
            if self.step_pbar:
                self.step_pbar.close()

            self.step_pbar = tqdm(
                total=100,
                desc=f"Epoch {epoch}",
                position=1,
                leave=False,
                bar_format='{desc}: {percentage:3.0f}%|{bar}| {n}/{total} [{elapsed}<{remaining}, loss={postfix[loss]:.6f}, acc={postfix[acc]:.4f}]',
                postfix={'loss': self.current_loss, 'acc': self.current_acc}
            )

    def update_step_progress(self, step, total_steps, loss=None, acc=None):
        """更新step进度"""
        if total_steps != self.total_steps:
            self.total_steps = total_steps
            if self.step_pbar:
                self.step_pbar.total = total_steps

        self.current_step = step
        if loss is not None:
            self.current_loss = loss
        if acc is not None:
            self.current_acc = acc

        if self.step_pbar:
            self.step_pbar.n = step
            self.step_pbar.set_postfix({'loss': self.current_loss, 'acc': self.current_acc})
            self.step_pbar.refresh()

    def update_metrics(self, loss=None, acc=None, f1=None, precision=None, recall=None):
        """更新指标显示"""
        if loss is not None:
            self.current_loss = loss
        if acc is not None:
            self.current_acc = acc

        # 在epoch完成时显示详细指标
        if f1 is not None and precision is not None and recall is not None:
            metrics_str = f"Loss={loss:.4f} | Acc={acc:.4f} | F1={f1:.4f} | Precision={precision:.4f} | Recall={recall:.4f}"
            tqdm.write(f"Epoch {self.current_epoch}: {metrics_str}")

    def close_progress_bars(self):
        """关闭进度条"""
        if self.step_pbar:
            self.step_pbar.close()
        if self.epoch_pbar:
            self.epoch_pbar.close()

    def stop(self):
        """停止监控"""
        self.running = False
        self.close_progress_bars()

def run_training_with_verbose_output():
    """运行训练并提供详细输出"""
    print("🚀 启动增强版训练监控")
    print("=" * 80)
    
    # 检查必要文件
    required_files = [
        "/home/<USER>/ET-BERT/output/datasets/train_dataset.tsv",
        "/home/<USER>/ET-BERT/output/datasets/valid_dataset.tsv",
        "/home/<USER>/ET-BERT/output/datasets/test_dataset.tsv",
        "models/pretrained_model.bin",
        "models/encryptd_vocab.txt"
    ]
    
    print("🔍 检查必要文件...")
    for file_path in required_files:
        if os.path.exists(file_path):
            size_mb = os.path.getsize(file_path) / (1024*1024)
            print(f"  ✅ {os.path.basename(file_path)}: {size_mb:.2f} MB")
        else:
            print(f"  ❌ {file_path}: 不存在")
            return False
    
    # 构建训练命令
    train_cmd = [
        sys.executable, "fine-tuning/run_classifier.py",
        "--pretrained_model_path", "models/pretrained_model.bin",
        "--vocab_path", "models/encryptd_vocab.txt",
        "--train_path", "/home/<USER>/ET-BERT/output/datasets/train_dataset.tsv",
        "--dev_path", "/home/<USER>/ET-BERT/output/datasets/valid_dataset.tsv",
        "--test_path", "/home/<USER>/ET-BERT/output/datasets/test_dataset.tsv",
        "--epochs_num", "100",
        "--batch_size", "64",  # 减少批次大小以避免内存问题
        "--embedding", "word_pos_seg",
        "--encoder", "transformer",
        "--mask", "fully_visible",
        "--seq_length", "128",
        "--learning_rate", "2e-3",
        "--output_model_path", "models/finetuned_model.bin"
    ]
    
    print("\n📝 训练命令:")
    print("  " + " ".join(train_cmd))
    print()
    
    # 创建监控器
    monitor = TrainingMonitor()
    
    try:
        print("🔄 启动训练进程...")
        print("💡 使用连续进度条显示训练进度")
        print("⚠️  如需中断，请按 Ctrl+C")
        print("=" * 80)

        # 创建进度条
        monitor.create_progress_bars()

        # 启动训练进程
        process = subprocess.Popen(
            train_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=0,  # 无缓冲
            preexec_fn=os.setsid if hasattr(os, 'setsid') else None
        )

        # 实时输出处理
        while True:
            try:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    line = output.strip()
                    monitor.line_count += 1

                    # 过滤掉PyTorch的常见警告
                    skip_warnings = [
                        "Was asked to gather along dimension 0",
                        "warnings.warn('Was asked to gather",
                        "_functions.py:68: UserWarning"
                    ]

                    # 如果是需要跳过的警告，跳过不显示
                    if any(warning in line for warning in skip_warnings):
                        continue

                    # 解析训练进度信息
                    epoch_match = re.search(r'Epoch id:\s*(\d+)', line)
                    if epoch_match:
                        epoch = int(epoch_match.group(1))
                        monitor.update_epoch_progress(epoch, monitor.total_epochs)

                    # 解析训练步骤
                    step_match = re.search(r'Training steps:\s*(\d+)', line)
                    if step_match:
                        step = int(step_match.group(1))
                        # 假设每个epoch有800步（根据之前的输出）
                        monitor.update_step_progress(step % 800, 800)

                    # 解析损失值
                    loss_match = re.search(r'Avg loss:\s*([\d.]+)', line)
                    if loss_match:
                        loss = float(loss_match.group(1))
                        monitor.update_step_progress(monitor.current_step, monitor.total_steps, loss=loss)

                    # 解析准确率
                    acc_match = re.search(r'Acc\.\s*\(Correct/Total\):\s*([\d.]+)', line)
                    if acc_match:
                        acc = float(acc_match.group(1))
                        monitor.update_metrics(acc=acc)

                        # 显示epoch完成信息
                        tqdm.write(f"Epoch {monitor.current_epoch}: Loss={monitor.current_loss:.4f} | Acc={acc:.4f}")

                    # 检查错误信息
                    error_keywords = ['error', 'failed', 'exception', 'traceback']
                    if any(keyword in line.lower() for keyword in error_keywords):
                        tqdm.write(f"❌ 错误: {line}")

                    # 检查重要信息（但不显示常规训练信息）
                    important_keywords = ['start training', 'training complete', 'best result', 'test set evaluation']
                    if any(keyword in line.lower() for keyword in important_keywords):
                        tqdm.write(f"🎯 {line}")

                else:
                    # 如果没有输出，短暂等待
                    time.sleep(0.1)

            except KeyboardInterrupt:
                tqdm.write(f"\n⚠️  收到中断信号，正在终止训练进程...")
                process.terminate()
                time.sleep(2)
                if process.poll() is None:
                    tqdm.write("强制终止进程...")
                    process.kill()
                break
        
        # 获取返回码
        return_code = process.poll()
        
        print(f"\n📊 训练完成!")
        print(f"  返回码: {return_code}")
        print(f"  总运行时间: {(time.time() - monitor.start_time)/60:.1f} 分钟")
        print(f"  总输出行数: {monitor.line_count}")
        
        if return_code == 0:
            print("✅ 训练成功完成!")
            
            # 检查输出模型
            model_path = "models/finetuned_model.bin"
            if os.path.exists(model_path):
                model_size = os.path.getsize(model_path) / (1024*1024)
                print(f"📦 微调模型已生成: {model_size:.2f} MB")
            
            return True
        else:
            print(f"❌ 训练失败，返回码: {return_code}")
            return False
            
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        return False
    finally:
        monitor.stop()

def show_system_info():
    """显示系统信息"""
    print("💻 系统信息:")
    
    try:
        import psutil
        memory = psutil.virtual_memory()
        print(f"  内存: {memory.percent:.1f}% 使用 ({memory.available/1024**3:.1f}GB 可用)")
        
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"  CPU: {cpu_percent:.1f}% 使用")
        
    except ImportError:
        print("  (需要安装 psutil 来显示详细系统信息)")
    
    try:
        import torch
        if torch.cuda.is_available():
            print(f"  GPU: {torch.cuda.device_count()} 个可用")
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                print(f"    GPU {i}: {gpu_name}")
        else:
            print("  GPU: 不可用，使用CPU训练")
    except ImportError:
        print("  (需要安装 torch 来检查GPU信息)")

def main():
    print("🚀 ET-BERT 增强版训练监控")
    print("=" * 60)
    
    # 显示系统信息
    show_system_info()
    print()
    
    # 运行训练
    success = run_training_with_verbose_output()
    
    if success:
        print("\n🎉 训练完成! 现在可以运行推理:")
        print("  python inference/run_classifier_infer.py [参数...]")
        return 0
    else:
        print("\n❌ 训练失败")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 用户中断程序")
        sys.exit(1)
