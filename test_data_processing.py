#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
测试数据处理流程
"""

import sys
import os

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    
    try:
        import data_process.dataset_generation as dg
        print("✅ dataset_generation 导入成功")
        
        # 测试关键函数是否存在
        if hasattr(dg, 'generation'):
            print("✅ generation 函数存在")
        else:
            print("❌ generation 函数不存在")
            return False
            
        if hasattr(dg, 'obtain_data'):
            print("✅ obtain_data 函数存在")
        else:
            print("❌ obtain_data 函数不存在")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    
    try:
        import data_process.dataset_generation as dg
        
        # 测试cut函数
        test_string = "abcdefgh"
        result = dg.cut(test_string, 2)
        print(f"✅ cut函数测试: {result}")
        
        # 测试bigram_generation函数
        test_hex = "48656c6c6f"
        result = dg.bigram_generation(test_hex, packet_len=5)
        print(f"✅ bigram_generation函数测试: {result[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def main():
    print("🚀 数据处理模块测试")
    print("="*50)
    
    # 测试导入
    if not test_imports():
        print("❌ 导入测试失败")
        return 1
    
    # 测试基本功能
    if not test_basic_functionality():
        print("❌ 功能测试失败")
        return 1
    
    print("\n✅ 所有测试通过!")
    print("现在可以运行数据处理了:")
    print("  python run_netmamba_dataset.py --step preprocess")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
