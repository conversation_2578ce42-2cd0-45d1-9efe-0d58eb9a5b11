#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
ET-BERT训练 - 简化进度条版本
稳定的进度条显示，避免复杂的格式化问题
"""

import os
import sys
import subprocess
import time
import re
from tqdm import tqdm

def run_training_with_simple_progress():
    """运行带简单进度条的训练"""
    
    # 检查必要文件
    required_files = [
        "/home/<USER>/ET-BERT/output/datasets/train_dataset.tsv",
        "/home/<USER>/ET-BERT/output/datasets/valid_dataset.tsv",
        "models/pretrained_model.bin"
    ]
    
    print("🔍 检查必要文件...")
    for file_path in required_files:
        if os.path.exists(file_path):
            size_mb = os.path.getsize(file_path) / (1024*1024)
            print(f"  ✅ {os.path.basename(file_path)}: {size_mb:.2f} MB")
        else:
            print(f"  ❌ {file_path}: 不存在")
            return False
    
    # 构建训练命令
    train_cmd = [
        sys.executable, "fine-tuning/run_classifier.py",
        "--pretrained_model_path", "models/pretrained_model.bin",
        "--vocab_path", "models/encryptd_vocab.txt",
        "--train_path", "/home/<USER>/ET-BERT/output/datasets/train_dataset.tsv",
        "--dev_path", "/home/<USER>/ET-BERT/output/datasets/valid_dataset.tsv",
        "--test_path", "/home/<USER>/ET-BERT/output/datasets/test_dataset.tsv",
        "--epochs_num", "100",
        "--batch_size", "64",
        "--embedding", "word_pos_seg",
        "--encoder", "transformer",
        "--mask", "fully_visible",
        "--seq_length", "128",
        "--learning_rate", "2e-3",
        "--output_model_path", "models/finetuned_model.bin"
    ]
    
    print("\n📝 训练命令:")
    print("  " + " ".join(train_cmd))
    print()
    
    # 训练状态变量
    current_epoch = 0
    total_epochs = 100
    current_step = 0
    total_steps = 800  # 估计值
    current_loss = 0.0
    best_acc = 0.0
    start_time = time.time()
    
    # 创建总体进度条
    print("🚀 ET-BERT Training Progress")
    print("=" * 80)
    
    epoch_pbar = tqdm(
        total=total_epochs,
        desc="[ET-BERT] Training",
        position=0,
        leave=True
    )
    
    step_pbar = None
    
    try:
        # 启动训练进程
        process = subprocess.Popen(
            train_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=0
        )
        
        # 解析输出并更新进度条
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            
            if output:
                line = output.strip()
                
                # 跳过警告信息
                skip_patterns = [
                    "Was asked to gather along dimension 0",
                    "warnings.warn",
                    "UserWarning"
                ]
                if any(pattern in line for pattern in skip_patterns):
                    continue
                
                # 解析epoch信息
                epoch_match = re.search(r'Epoch id:\s*(\d+)', line)
                if epoch_match:
                    new_epoch = int(epoch_match.group(1))
                    if new_epoch != current_epoch:
                        # 关闭旧的step进度条
                        if step_pbar:
                            step_pbar.close()
                        
                        current_epoch = new_epoch
                        current_step = 0
                        
                        # 更新epoch进度条
                        epoch_pbar.n = current_epoch
                        epoch_pbar.set_description(f"[ET-BERT] Epoch {current_epoch}/{total_epochs}")
                        epoch_pbar.refresh()
                        
                        # 创建新的step进度条
                        step_pbar = tqdm(
                            total=total_steps,
                            desc=f"  Steps",
                            position=1,
                            leave=False
                        )
                
                # 解析训练步骤和损失
                step_loss_match = re.search(r'Training steps:\s*(\d+).*Avg loss:\s*([\d.]+)', line)
                if step_loss_match:
                    step = int(step_loss_match.group(1))
                    loss = float(step_loss_match.group(2))
                    current_loss = loss
                    
                    # 将step映射到当前epoch内的进度
                    epoch_step = step % total_steps
                    if epoch_step == 0:
                        epoch_step = total_steps
                    
                    if step_pbar:
                        step_pbar.n = epoch_step
                        step_pbar.set_description(f"  Steps (loss={loss:.4f})")
                        step_pbar.refresh()
                
                # 解析epoch结束时的准确率
                acc_match = re.search(r'Acc\.\s*\(Correct/Total\):\s*([\d.]+)', line)
                if acc_match:
                    acc = float(acc_match.group(1))
                    
                    if acc > best_acc:
                        best_acc = acc
                        best_indicator = " 🎯 New Best!"
                    else:
                        best_indicator = ""
                    
                    # 完成当前epoch的step进度条
                    if step_pbar:
                        step_pbar.n = total_steps
                        step_pbar.refresh()
                        step_pbar.close()
                        step_pbar = None
                    
                    # 显示epoch结果
                    elapsed = (time.time() - start_time) / 60
                    result_str = f"Epoch {current_epoch}: Loss={current_loss:.4f} | Acc={acc:.4f} | Best={best_acc:.4f} | Time={elapsed:.1f}min{best_indicator}"
                    tqdm.write(result_str)
                    
                    # 更新总体进度条描述
                    epoch_pbar.set_description(f"[ET-BERT] Best Acc: {best_acc:.4f}")
                
                # 检查错误
                if any(keyword in line.lower() for keyword in ['error', 'exception', 'failed']):
                    tqdm.write(f"❌ {line}")
                
                # 检查重要信息
                if any(keyword in line.lower() for keyword in ['start training', 'best result', 'complete']):
                    tqdm.write(f"🎯 {line}")
        
        # 获取返回码
        return_code = process.poll()
        
        # 关闭进度条
        if step_pbar:
            step_pbar.close()
        epoch_pbar.close()
        
        # 显示结果
        total_time = (time.time() - start_time) / 60
        print(f"\n📊 训练完成!")
        print(f"  返回码: {return_code}")
        print(f"  运行时间: {total_time:.1f} 分钟")
        print(f"  最佳准确率: {best_acc:.4f}")
        
        if return_code == 0:
            print("✅ 训练成功!")
            
            # 检查模型文件
            model_path = "models/finetuned_model.bin"
            if os.path.exists(model_path):
                model_size = os.path.getsize(model_path) / (1024*1024)
                print(f"📦 模型已保存: {model_size:.2f} MB")
            
            return True
        else:
            print("❌ 训练失败")
            return False
            
    except KeyboardInterrupt:
        if step_pbar:
            step_pbar.close()
        epoch_pbar.close()
        print(f"\n⚠️  训练被用户中断")
        process.terminate()
        return False
    except Exception as e:
        if step_pbar:
            step_pbar.close()
        epoch_pbar.close()
        print(f"❌ 训练出错: {e}")
        return False

def main():
    print("🚀 ET-BERT 简化进度条训练")
    print("=" * 60)
    
    if run_training_with_simple_progress():
        print("\n🎉 训练完成! 现在可以运行推理:")
        print("  python run_inference_only.py")
        return 0
    else:
        print("\n❌ 训练失败")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 用户中断程序")
        sys.exit(1)
