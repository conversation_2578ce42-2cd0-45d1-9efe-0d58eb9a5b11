#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
诊断训练卡住的问题
"""

import os
import sys
import pandas as pd
import numpy as np
import psutil
import torch

def check_system_resources():
    """检查系统资源"""
    print("🔍 检查系统资源...")
    
    # 内存使用情况
    memory = psutil.virtual_memory()
    print(f"  💾 内存使用: {memory.percent:.1f}% ({memory.used/1024**3:.1f}GB / {memory.total/1024**3:.1f}GB)")
    
    # CPU使用情况
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"  🖥️  CPU使用: {cpu_percent:.1f}%")
    
    # 磁盘空间
    disk = psutil.disk_usage('/')
    print(f"  💿 磁盘使用: {disk.percent:.1f}% ({disk.used/1024**3:.1f}GB / {disk.total/1024**3:.1f}GB)")
    
    # GPU信息
    if torch.cuda.is_available():
        print(f"  🎮 GPU可用: {torch.cuda.device_count()} 个")
        for i in range(torch.cuda.device_count()):
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"    GPU {i}: {torch.cuda.get_device_name(i)} ({gpu_memory:.1f}GB)")
    else:
        print("  🎮 GPU: 不可用，使用CPU")

def check_tsv_files():
    """检查TSV文件"""
    print("\n📋 检查TSV文件...")
    
    tsv_files = {
        "训练集": "/home/<USER>/ET-BERT/output/datasets/train_dataset.tsv",
        "验证集": "/home/<USER>/ET-BERT/output/datasets/valid_dataset.tsv",
        "测试集": "/home/<USER>/ET-BERT/output/datasets/test_dataset.tsv",
        "推理集": "/home/<USER>/ET-BERT/output/datasets/nolabel_test_dataset.tsv"
    }
    
    for name, path in tsv_files.items():
        if os.path.exists(path):
            try:
                # 检查文件大小
                file_size = os.path.getsize(path) / (1024*1024)  # MB
                print(f"  📄 {name}: {file_size:.2f} MB")
                
                # 检查文件内容
                df = pd.read_csv(path, sep='\t', nrows=5)  # 只读前5行
                print(f"    列名: {list(df.columns)}")
                print(f"    样本预览: {len(df)} 行")
                
                # 检查是否有空值或异常
                if 'label' in df.columns:
                    labels = df['label'].unique()
                    print(f"    标签范围: {min(labels)} - {max(labels)} (共{len(labels)}个)")
                
                if 'text_a' in df.columns:
                    text_lengths = df['text_a'].str.len()
                    print(f"    文本长度: 平均{text_lengths.mean():.0f}, 最大{text_lengths.max()}")
                
            except Exception as e:
                print(f"  ❌ {name}: 读取错误 - {e}")
        else:
            print(f"  ❌ {name}: 文件不存在")

def test_data_loading():
    """测试数据加载"""
    print("\n🧪 测试数据加载...")
    
    train_path = "/home/<USER>/ET-BERT/output/datasets/train_dataset.tsv"
    
    if not os.path.exists(train_path):
        print("❌ 训练文件不存在")
        return False
    
    try:
        print("  📖 读取训练数据...")
        df = pd.read_csv(train_path, sep='\t')
        print(f"    ✅ 成功读取 {len(df)} 行数据")
        
        # 检查数据质量
        print("  🔍 检查数据质量...")
        
        # 检查空值
        null_counts = df.isnull().sum()
        if null_counts.any():
            print(f"    ⚠️  发现空值: {dict(null_counts[null_counts > 0])}")
        else:
            print("    ✅ 无空值")
        
        # 检查标签分布
        if 'label' in df.columns:
            label_counts = df['label'].value_counts().sort_index()
            print(f"    📊 标签分布: {dict(label_counts)}")
            
            # 检查是否有类别不平衡
            min_samples = label_counts.min()
            max_samples = label_counts.max()
            if max_samples / min_samples > 10:
                print(f"    ⚠️  类别不平衡严重: 最少{min_samples}样本, 最多{max_samples}样本")
        
        # 检查文本长度分布
        if 'text_a' in df.columns:
            text_lengths = df['text_a'].str.len()
            print(f"    📏 文本长度统计:")
            print(f"      平均: {text_lengths.mean():.0f}")
            print(f"      中位数: {text_lengths.median():.0f}")
            print(f"      最小: {text_lengths.min()}")
            print(f"      最大: {text_lengths.max()}")
            
            # 检查是否有异常长的文本
            if text_lengths.max() > 10000:
                print(f"    ⚠️  发现异常长的文本 (>{text_lengths.max()}字符)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据加载失败: {e}")
        return False

def test_model_files():
    """测试模型文件"""
    print("\n🤖 检查模型文件...")
    
    model_files = {
        "预训练模型": "models/pretrained_model.bin",
        "词汇表": "models/encryptd_vocab.txt",
        "配置文件": "models/bert_base_config.json"
    }
    
    all_good = True
    for name, path in model_files.items():
        if os.path.exists(path):
            file_size = os.path.getsize(path) / (1024*1024)  # MB
            print(f"  ✅ {name}: {file_size:.2f} MB")
            
            # 特殊检查
            if name == "词汇表":
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        vocab_lines = sum(1 for _ in f)
                    print(f"    词汇量: {vocab_lines}")
                except:
                    print(f"    ⚠️  无法读取词汇表")
            
        else:
            print(f"  ❌ {name}: 不存在")
            all_good = False
    
    return all_good

def create_small_test_dataset():
    """创建小型测试数据集"""
    print("\n🧪 创建小型测试数据集...")
    
    try:
        # 读取原始数据
        train_path = "/home/<USER>/ET-BERT/output/datasets/train_dataset.tsv"
        if not os.path.exists(train_path):
            print("❌ 原始训练数据不存在")
            return False
        
        df = pd.read_csv(train_path, sep='\t')
        
        # 创建小型数据集 (每个类别10个样本)
        small_df_list = []
        for label in df['label'].unique():
            label_df = df[df['label'] == label].head(10)
            small_df_list.append(label_df)
        
        small_df = pd.concat(small_df_list, ignore_index=True)
        
        # 保存小型数据集
        small_train_path = "/home/<USER>/ET-BERT/output/datasets/small_train_dataset.tsv"
        small_df.to_csv(small_train_path, sep='\t', index=False)
        
        print(f"  ✅ 小型训练集创建完成: {len(small_df)} 样本")
        print(f"  📁 保存位置: {small_train_path}")
        
        # 也创建小型验证集和测试集
        small_valid_path = "/home/<USER>/ET-BERT/output/datasets/small_valid_dataset.tsv"
        small_test_path = "/home/<USER>/ET-BERT/output/datasets/small_test_dataset.tsv"
        
        small_df.to_csv(small_valid_path, sep='\t', index=False)
        small_df.to_csv(small_test_path, sep='\t', index=False)
        
        print(f"  ✅ 小型验证集和测试集也已创建")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 创建小型数据集失败: {e}")
        return False

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 建议的解决方案:")
    
    print("1. 🔧 减少批次大小:")
    print("   修改训练命令中的 --batch_size 从 32 改为 16 或 8")
    
    print("\n2. 📏 减少序列长度:")
    print("   修改训练命令中的 --seq_length 从 128 改为 64")
    
    print("\n3. 🧪 使用小型数据集测试:")
    print("   python train_small_dataset.py")
    
    print("\n4. 💾 释放内存:")
    print("   重启Python环境或清理不必要的进程")
    
    print("\n5. 🎮 强制使用CPU:")
    print("   export CUDA_VISIBLE_DEVICES=\"\"")
    print("   然后重新运行训练")

def create_small_training_script():
    """创建小型数据集训练脚本"""
    script_content = '''#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
使用小型数据集进行训练测试
"""

import subprocess
import sys

def main():
    print("🧪 使用小型数据集进行训练测试...")
    
    train_cmd = [
        sys.executable, "fine-tuning/run_classifier.py",
        "--pretrained_model_path", "models/pretrained_model.bin",
        "--vocab_path", "models/encryptd_vocab.txt",
        "--train_path", "/home/<USER>/ET-BERT/output/datasets/small_train_dataset.tsv",
        "--dev_path", "/home/<USER>/ET-BERT/output/datasets/small_valid_dataset.tsv",
        "--test_path", "/home/<USER>/ET-BERT/output/datasets/small_test_dataset.tsv",
        "--epochs_num", "3",  # 减少训练轮数
        "--batch_size", "8",  # 减少批次大小
        "--embedding", "word_pos_seg",
        "--encoder", "transformer",
        "--mask", "fully_visible",
        "--seq_length", "64",  # 减少序列长度
        "--learning_rate", "2e-5",
        "--output_model_path", "models/small_finetuned_model.bin"
    ]
    
    print("📝 小型训练命令:")
    print("  " + " ".join(train_cmd))
    
    try:
        result = subprocess.run(train_cmd, timeout=600)  # 10分钟超时
        if result.returncode == 0:
            print("✅ 小型数据集训练成功!")
        else:
            print("❌ 小型数据集训练失败")
    except subprocess.TimeoutExpired:
        print("⏰ 训练超时")
    except Exception as e:
        print(f"❌ 训练出错: {e}")

if __name__ == "__main__":
    main()
'''
    
    with open("train_small_dataset.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("  ✅ 小型训练脚本已创建: train_small_dataset.py")

def main():
    print("🚀 ET-BERT 训练问题诊断")
    print("=" * 60)
    
    # 检查系统资源
    check_system_resources()
    
    # 检查TSV文件
    check_tsv_files()
    
    # 测试数据加载
    data_ok = test_data_loading()
    
    # 检查模型文件
    model_ok = test_model_files()
    
    # 创建小型测试数据集
    if data_ok:
        create_small_test_dataset()
        create_small_training_script()
    
    # 建议解决方案
    suggest_solutions()
    
    print("\n🎯 诊断完成!")
    
    if not data_ok or not model_ok:
        print("❌ 发现问题，请根据上述信息修复")
        return 1
    else:
        print("✅ 基本检查通过，建议尝试小型数据集训练")
        return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
