# NetMamba数据集ET-BERT适配使用指南

## 📋 概述

本指南专门针对您在 `/home/<USER>/NetMamba/dataset/database_clean` 路径下的数据集，帮助您快速将数据适配到ET-BERT模型进行流量分类。

## 🛠️ 已完成的配置修改

我已经为您修改了以下文件以适配您的数据集：

### 1. `data_process/main.py`
- ✅ 修改了数据集路径为 `/home/<USER>/NetMamba/dataset/database_clean/`
- ✅ 修改了输出路径为 `/home/<USER>/NetMamba/output/result/`
- ✅ 修改了所有路径分隔符为Linux格式 (`/`)
- ✅ 设置了默认参数（每类1000样本，包级别分析）

### 2. `data_process/open_dataset_deal.py`
- ✅ 修改了路径分隔符为Linux格式

### 3. `data_process/dataset_generation.py`
- ✅ 修改了输出路径为 `/home/<USER>/NetMamba/output/corpora/`
- ✅ 修改了所有路径分隔符为Linux格式
- ✅ 改进了目录创建逻辑

### 4. 创建了专用运行脚本
- ✅ `run_netmamba_dataset.py` - 针对您数据集的一键运行脚本

## 🚀 快速开始

### 方法1: 一键运行（推荐）

```bash
# 在ET-BERT项目根目录下运行
python run_netmamba_dataset.py --step all
```

### 方法2: 分步执行

```bash
# 步骤1: 数据预处理
python run_netmamba_dataset.py --step preprocess

# 步骤2: 模型训练
python run_netmamba_dataset.py --step train

# 步骤3: 模型推理
python run_netmamba_dataset.py --step inference
```

### 方法3: 自定义参数

```bash
# 指定每个类别的样本数量
python run_netmamba_dataset.py --samples_per_class 500 --step all

# 跳过数据集检查（如果检查有问题）
python run_netmamba_dataset.py --skip_check --step all
```

## 📁 输出文件结构

运行完成后，您将在以下位置找到输出文件：

```
/home/<USER>/NetMamba/output/
├── result/                          # 数据处理结果
│   ├── dataset/                     # numpy格式数据集
│   │   ├── x_datagram_train.npy
│   │   ├── x_datagram_test.npy
│   │   ├── x_datagram_valid.npy
│   │   ├── y_train.npy
│   │   ├── y_test.npy
│   │   └── y_valid.npy
│   └── dataset.json                 # 原始特征数据
├── datasets/                        # TSV格式训练数据
│   ├── train_dataset.tsv
│   ├── valid_dataset.tsv
│   ├── test_dataset.tsv
│   └── nolabel_test_dataset.tsv
├── corpora/                         # 语料库文件
│   └── encrypted_burst.txt
└── prediction.tsv                   # 最终预测结果
```

## ⚙️ 参数调整

如果需要调整参数，可以修改 `data_process/main.py` 中的以下设置：

```python
# 类别数量（脚本会自动检测，但也可以手动设置）
_category = 120  # 根据您的实际类别数修改

# 每个类别的样本数量
samples = [1000]  # 可以调整为 [500], [2000] 等

# 数据级别
dataset_level = "packet"  # 或 "flow"

# 特征类型
features = ["payload"]  # 或 ["payload", "length", "time", "direction"]
```

## 🔧 故障排除

### 常见问题

1. **内存不足**
   ```bash
   # 减少样本数量
   python run_netmamba_dataset.py --samples_per_class 500
   ```

2. **数据集路径问题**
   ```bash
   # 检查数据集是否存在
   ls -la /home/<USER>/NetMamba/dataset/database_clean/
   ```

3. **权限问题**
   ```bash
   # 确保有写入权限
   chmod -R 755 /home/<USER>/NetMamba/output/
   ```

4. **依赖包缺失**
   ```bash
   # 安装必要的包
   pip install scapy numpy scikit-learn pandas tqdm torch
   ```

### 检查数据集结构

运行以下命令检查您的数据集结构：

```bash
python -c "
import os
dataset_path = '/home/<USER>/NetMamba/dataset/database_clean'
print('数据集结构:')
for item in os.listdir(dataset_path):
    item_path = os.path.join(dataset_path, item)
    if os.path.isdir(item_path):
        pcap_count = len([f for f in os.listdir(item_path) if f.endswith(('.pcap', '.pcapng'))])
        print(f'  {item}: {pcap_count} 个PCAP文件')
"
```

## 📊 监控进度

运行过程中，您可以通过以下方式监控进度：

```bash
# 查看输出目录
watch -n 5 "ls -la /home/<USER>/NetMamba/output/"

# 查看日志（如果有的话）
tail -f /home/<USER>/NetMamba/output/processing.log
```

## 🎯 预期结果

成功运行后，您应该看到：

1. ✅ 数据预处理完成，生成TSV格式训练数据
2. ✅ 模型训练完成，生成微调后的模型文件
3. ✅ 推理完成，生成预测结果文件

## 📞 获取帮助

如果遇到问题：

1. 检查错误信息和日志输出
2. 确认数据集路径和格式正确
3. 验证系统资源（内存、磁盘空间）是否充足
4. 检查Python环境和依赖包是否完整

## 🔄 重新运行

如果需要重新运行：

```bash
# 清理输出目录
rm -rf /home/<USER>/NetMamba/output/*

# 重新运行
python run_netmamba_dataset.py --step all
```

---

**注意**: 整个处理过程可能需要较长时间，具体取决于您的数据集大小和系统性能。建议在服务器上运行，并确保有足够的计算资源。
