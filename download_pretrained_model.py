#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
下载ET-BERT预训练模型
"""

import os
import urllib.request
import shutil
from pathlib import Path

def download_file(url, local_path, description="文件"):
    """下载文件并显示进度"""
    print(f"📥 下载{description}...")
    print(f"  URL: {url}")
    print(f"  保存到: {local_path}")
    
    try:
        # 创建目录
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        def progress_hook(block_num, block_size, total_size):
            if total_size > 0:
                percent = min(100, (block_num * block_size * 100) // total_size)
                downloaded = block_num * block_size
                total_mb = total_size / (1024 * 1024)
                downloaded_mb = downloaded / (1024 * 1024)
                print(f"\r  进度: {percent:3d}% ({downloaded_mb:.1f}/{total_mb:.1f} MB)", end="", flush=True)
        
        urllib.request.urlretrieve(url, local_path, progress_hook)
        print(f"\n  ✅ {description}下载完成")
        
        # 检查文件大小
        file_size = os.path.getsize(local_path) / (1024 * 1024)
        print(f"  📊 文件大小: {file_size:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"\n  ❌ 下载失败: {e}")
        return False

def create_mock_pretrained_model():
    """创建一个模拟的预训练模型文件"""
    print("🔧 创建模拟预训练模型...")
    
    model_path = "models/pretrained_model.bin"
    
    try:
        # 创建目录
        os.makedirs("models", exist_ok=True)
        
        # 创建一个基本的模型文件结构
        # 这里我们创建一个包含基本BERT参数的文件
        import torch
        
        # 基本的BERT配置
        model_config = {
            'vocab_size': 21128,
            'hidden_size': 768,
            'num_hidden_layers': 12,
            'num_attention_heads': 12,
            'intermediate_size': 3072,
            'hidden_act': 'gelu',
            'hidden_dropout_prob': 0.1,
            'attention_probs_dropout_prob': 0.1,
            'max_position_embeddings': 512,
            'type_vocab_size': 2,
            'initializer_range': 0.02
        }
        
        # 创建基本的模型状态字典
        state_dict = {}
        
        # 嵌入层
        state_dict['embedding.word.weight'] = torch.randn(model_config['vocab_size'], model_config['hidden_size'])
        state_dict['embedding.position.weight'] = torch.randn(model_config['max_position_embeddings'], model_config['hidden_size'])
        state_dict['embedding.segment.weight'] = torch.randn(model_config['type_vocab_size'], model_config['hidden_size'])
        state_dict['embedding.layer_norm.weight'] = torch.ones(model_config['hidden_size'])
        state_dict['embedding.layer_norm.bias'] = torch.zeros(model_config['hidden_size'])
        
        # Transformer层
        for i in range(model_config['num_hidden_layers']):
            # 自注意力
            state_dict[f'encoder.transformer.{i}.self_attn.linear_layers.0.weight'] = torch.randn(model_config['hidden_size'], model_config['hidden_size'])
            state_dict[f'encoder.transformer.{i}.self_attn.linear_layers.0.bias'] = torch.zeros(model_config['hidden_size'])
            state_dict[f'encoder.transformer.{i}.self_attn.linear_layers.1.weight'] = torch.randn(model_config['hidden_size'], model_config['hidden_size'])
            state_dict[f'encoder.transformer.{i}.self_attn.linear_layers.1.bias'] = torch.zeros(model_config['hidden_size'])
            state_dict[f'encoder.transformer.{i}.self_attn.linear_layers.2.weight'] = torch.randn(model_config['hidden_size'], model_config['hidden_size'])
            state_dict[f'encoder.transformer.{i}.self_attn.linear_layers.2.bias'] = torch.zeros(model_config['hidden_size'])
            state_dict[f'encoder.transformer.{i}.self_attn.final_linear.weight'] = torch.randn(model_config['hidden_size'], model_config['hidden_size'])
            state_dict[f'encoder.transformer.{i}.self_attn.final_linear.bias'] = torch.zeros(model_config['hidden_size'])
            
            # 前馈网络
            state_dict[f'encoder.transformer.{i}.feed_forward.linear_1.weight'] = torch.randn(model_config['intermediate_size'], model_config['hidden_size'])
            state_dict[f'encoder.transformer.{i}.feed_forward.linear_1.bias'] = torch.zeros(model_config['intermediate_size'])
            state_dict[f'encoder.transformer.{i}.feed_forward.linear_2.weight'] = torch.randn(model_config['hidden_size'], model_config['intermediate_size'])
            state_dict[f'encoder.transformer.{i}.feed_forward.linear_2.bias'] = torch.zeros(model_config['hidden_size'])
            
            # 层归一化
            state_dict[f'encoder.transformer.{i}.layer_norm_1.weight'] = torch.ones(model_config['hidden_size'])
            state_dict[f'encoder.transformer.{i}.layer_norm_1.bias'] = torch.zeros(model_config['hidden_size'])
            state_dict[f'encoder.transformer.{i}.layer_norm_2.weight'] = torch.ones(model_config['hidden_size'])
            state_dict[f'encoder.transformer.{i}.layer_norm_2.bias'] = torch.zeros(model_config['hidden_size'])
        
        # 保存模型
        torch.save(state_dict, model_path)
        
        file_size = os.path.getsize(model_path) / (1024 * 1024)
        print(f"  ✅ 模拟模型创建完成: {file_size:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 创建模拟模型失败: {e}")
        return False

def check_torch_available():
    """检查PyTorch是否可用"""
    try:
        import torch
        print(f"✅ PyTorch可用，版本: {torch.__version__}")
        return True
    except ImportError:
        print("❌ PyTorch不可用")
        return False

def download_from_alternatives():
    """尝试从多个源下载预训练模型"""
    print("🔍 尝试从多个源下载预训练模型...")
    
    # 可能的下载链接
    download_urls = [
        # 如果有官方链接，可以添加在这里
        # "https://example.com/pretrained_model.bin",
    ]
    
    model_path = "models/pretrained_model.bin"
    
    for i, url in enumerate(download_urls):
        print(f"\n尝试源 {i+1}/{len(download_urls)}:")
        if download_file(url, model_path, "预训练模型"):
            return True
    
    print("❌ 所有下载源都失败")
    return False

def main():
    print("🚀 ET-BERT 预训练模型下载器")
    print("=" * 50)
    
    model_path = "models/pretrained_model.bin"
    
    # 检查模型是否已存在
    if os.path.exists(model_path):
        file_size = os.path.getsize(model_path) / (1024 * 1024)
        print(f"✅ 预训练模型已存在: {file_size:.2f} MB")
        return 0
    
    print("📋 预训练模型获取选项:")
    print("1. 从网络下载官方预训练模型")
    print("2. 创建模拟预训练模型（用于测试）")
    print("3. 手动下载指导")
    
    choice = input("\n请选择 (1/2/3): ").strip()
    
    if choice == "1":
        # 尝试下载
        if not download_from_alternatives():
            print("\n⚠️  自动下载失败，建议选择选项2或3")
            return 1
    
    elif choice == "2":
        # 检查PyTorch
        if not check_torch_available():
            print("需要安装PyTorch: pip install torch")
            return 1
        
        # 创建模拟模型
        if not create_mock_pretrained_model():
            return 1
    
    elif choice == "3":
        print("\n📖 手动下载指导:")
        print("1. 访问ET-BERT官方仓库或相关资源")
        print("2. 下载预训练模型文件")
        print("3. 将文件保存为: models/pretrained_model.bin")
        print("4. 确保文件大小合理（通常几百MB）")
        print("\n或者您可以:")
        print("- 使用其他BERT预训练模型")
        print("- 重新运行此脚本选择选项2创建模拟模型")
        return 0
    
    else:
        print("❌ 无效选择")
        return 1
    
    # 验证模型文件
    if os.path.exists(model_path):
        file_size = os.path.getsize(model_path) / (1024 * 1024)
        print(f"\n✅ 预训练模型准备完成!")
        print(f"📊 文件大小: {file_size:.2f} MB")
        print(f"📁 文件路径: {model_path}")
        print("\n现在可以运行训练脚本:")
        print("  python train_and_evaluate.py")
        return 0
    else:
        print(f"\n❌ 预训练模型文件不存在: {model_path}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
