#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
ET-BERT 环境和配置测试脚本
用于验证环境是否正确配置
"""

import os
import sys
import subprocess
import importlib.util
from pathlib import Path

def test_python_dependencies():
    """测试Python依赖包"""
    print("🔍 检查Python依赖包...")
    
    required_packages = [
        'torch', 'numpy', 'scapy', 'sklearn', 'pandas', 
        'tqdm', 'argparse', 'json', 'pickle'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sklearn':
                import sklearn
            else:
                __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少以下包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    else:
        print("✅ 所有Python依赖包已安装")
        return True

def test_external_tools():
    """测试外部工具"""
    print("\n🔍 检查外部工具...")
    
    tools = {
        'tshark': 'tshark --version',
        'python': 'python --version'
    }
    
    # Windows特定工具
    if os.name == 'nt':
        tools['SplitCap'] = 'SplitCap.exe'
    
    missing_tools = []
    
    for tool, cmd in tools.items():
        try:
            result = subprocess.run(cmd.split(), 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=10)
            if result.returncode == 0:
                print(f"  ✅ {tool}")
            else:
                print(f"  ❌ {tool} (返回码: {result.returncode})")
                missing_tools.append(tool)
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            print(f"  ❌ {tool} (未找到或无法执行)")
            missing_tools.append(tool)
    
    if missing_tools:
        print(f"\n⚠️  缺少以下工具: {', '.join(missing_tools)}")
        return False
    else:
        print("✅ 所有外部工具可用")
        return True

def test_project_structure():
    """测试项目结构"""
    print("\n🔍 检查项目结构...")
    
    required_files = [
        'custom_dataset_adapter.py',
        'config_template.py',
        'run_custom_dataset.py',
        'fine-tuning/run_classifier.py',
        'inference/run_classifier_infer.py',
        'data_process/main.py',
        'data_process/dataset_generation.py',
        'uer/__init__.py'
    ]
    
    required_dirs = [
        'models',
        'data_process',
        'fine-tuning',
        'inference',
        'uer'
    ]
    
    missing_files = []
    missing_dirs = []
    
    # 检查文件
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"  ❌ {file_path}")
            missing_files.append(file_path)
        else:
            print(f"  ✅ {file_path}")
    
    # 检查目录
    for dir_path in required_dirs:
        if not Path(dir_path).is_dir():
            print(f"  ❌ {dir_path}/")
            missing_dirs.append(dir_path)
        else:
            print(f"  ✅ {dir_path}/")
    
    if missing_files or missing_dirs:
        print(f"\n⚠️  缺少文件: {missing_files}")
        print(f"⚠️  缺少目录: {missing_dirs}")
        return False
    else:
        print("✅ 项目结构完整")
        return True

def test_model_files():
    """测试模型文件"""
    print("\n🔍 检查模型文件...")
    
    model_files = [
        'models/encryptd_vocab.txt',
        'models/bert_base_config.json'
    ]
    
    optional_files = [
        'models/pretrained_model.bin',
        'pretrained_model.bin'
    ]
    
    missing_required = []
    missing_optional = []
    
    # 检查必需文件
    for file_path in model_files:
        if not Path(file_path).exists():
            print(f"  ❌ {file_path}")
            missing_required.append(file_path)
        else:
            print(f"  ✅ {file_path}")
    
    # 检查可选文件
    for file_path in optional_files:
        if not Path(file_path).exists():
            print(f"  ⚠️  {file_path} (可选)")
            missing_optional.append(file_path)
        else:
            print(f"  ✅ {file_path}")
    
    if missing_required:
        print(f"\n❌ 缺少必需的模型文件: {missing_required}")
        return False
    
    if missing_optional:
        print(f"\n⚠️  建议下载预训练模型以获得更好效果")
        print("下载命令: wget -O pretrained_model.bin [模型URL]")
    
    print("✅ 模型文件检查完成")
    return True

def test_config_file():
    """测试配置文件"""
    print("\n🔍 检查配置文件...")
    
    try:
        from config_template import get_config, validate_config
        config = get_config()
        
        if validate_config(config):
            print("  ✅ 配置文件格式正确")
            
            # 检查路径是否存在
            input_path = config.get('pcap_input_path')
            if input_path and Path(input_path).exists():
                print(f"  ✅ 输入路径存在: {input_path}")
            else:
                print(f"  ⚠️  输入路径不存在: {input_path}")
                print("     请在配置文件中设置正确的PCAP输入路径")
            
            return True
        else:
            print("  ❌ 配置文件验证失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 配置文件错误: {e}")
        return False

def test_sample_data():
    """检查是否有示例数据"""
    print("\n🔍 检查示例数据...")
    
    sample_paths = [
        'datasets',
        'corpora'
    ]
    
    found_data = False
    for path in sample_paths:
        if Path(path).exists() and any(Path(path).iterdir()):
            print(f"  ✅ 找到数据目录: {path}")
            found_data = True
        else:
            print(f"  ⚠️  数据目录为空或不存在: {path}")
    
    if not found_data:
        print("  ℹ️  未找到示例数据，请准备您自己的PCAP文件")
    
    return True

def main():
    """主测试函数"""
    print("🚀 ET-BERT 环境测试")
    print("=" * 50)
    
    tests = [
        ("Python依赖包", test_python_dependencies),
        ("外部工具", test_external_tools),
        ("项目结构", test_project_structure),
        ("模型文件", test_model_files),
        ("配置文件", test_config_file),
        ("示例数据", test_sample_data)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"  ❌ 测试 '{test_name}' 时出错: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! 环境配置正确，可以开始使用ET-BERT。")
        print("\n下一步:")
        print("1. 准备您的PCAP数据集")
        print("2. 修改 config_template.py 中的配置")
        print("3. 运行: python run_custom_dataset.py --step all")
        return True
    else:
        print("⚠️  部分测试失败，请根据上述提示修复问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
