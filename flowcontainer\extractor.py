#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
flowcontainer.extractor 替代模块
为ET-BERT提供基本的网络流提取功能
"""

import os
import random

class FlowFeatures:
    """模拟流特征类"""
    def __init__(self, pcap_file):
        self.pcap_file = pcap_file
        self.ip_lengths = []
        self.ip_timestamps = []
        self.extension = {}
        self._generate_mock_features()
    
    def _generate_mock_features(self):
        """生成模拟的流特征"""
        # 生成5-20个包的模拟数据
        num_packets = random.randint(5, 20)
        
        # 模拟包长度（正数表示出站，负数表示入站）
        for i in range(num_packets):
            if i % 2 == 0:
                # 出站包
                length = random.randint(60, 1500)
            else:
                # 入站包  
                length = -random.randint(60, 1500)
            self.ip_lengths.append(length)
        
        # 模拟时间戳
        base_time = 0.0
        for i in range(num_packets):
            base_time += random.uniform(0.001, 0.1)  # 1ms到100ms间隔
            self.ip_timestamps.append(base_time)
        
        # 模拟扩展信息（TLS相关）
        self.extension = {
            'tls.record.content_type': [
                (['22'], 1),  # Handshake
                (['23'], 3),  # Application Data
                (['23'], 5),  # Application Data
            ],
            'tls.handshake.type': [
                (['1'], 1),   # Client Hello
                (['2'], 2),   # Server Hello
            ]
        }

def extract(pcap_file, filter='tcp', extension=None):
    """
    提取网络流特征的替代函数
    
    Args:
        pcap_file: PCAP文件路径
        filter: 过滤器（tcp/udp等）
        extension: 扩展字段列表
    
    Returns:
        dict: 包含流特征的字典
    """
    print(f"📊 使用简化extractor处理: {os.path.basename(pcap_file)}")
    
    # 检查文件是否存在
    if not os.path.exists(pcap_file):
        print(f"⚠️  文件不存在: {pcap_file}")
        return {}
    
    # 检查文件大小
    file_size = os.path.getsize(pcap_file)
    if file_size == 0:
        print(f"⚠️  文件为空: {pcap_file}")
        return {}
    
    # 生成模拟的流标识符
    # 通常格式为 (文件名, 协议, 流ID)
    flow_key = (pcap_file, filter.lower(), '0')
    
    # 创建流特征对象
    flow_features = FlowFeatures(pcap_file)
    
    # 返回结果字典
    result = {
        flow_key: flow_features
    }
    
    return result

def extract_advanced(pcap_file, **kwargs):
    """
    高级提取函数（兼容性）
    """
    return extract(pcap_file, **kwargs)

# 兼容性别名
def flow_extract(pcap_file, filter='tcp'):
    """兼容性别名"""
    return extract(pcap_file, filter=filter)

if __name__ == "__main__":
    # 测试代码
    print("测试flowcontainer.extractor替代模块")
    
    # 创建一个测试文件
    test_file = "test.pcap"
    with open(test_file, "wb") as f:
        f.write(b"mock pcap data")
    
    try:
        result = extract(test_file)
        print(f"提取结果: {len(result)} 个流")
        
        for key, features in result.items():
            print(f"流 {key}:")
            print(f"  包数量: {len(features.ip_lengths)}")
            print(f"  时间跨度: {features.ip_timestamps[-1]:.3f}s")
            print(f"  扩展字段: {len(features.extension)} 个")
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
