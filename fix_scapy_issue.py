#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
修复Scapy在Python 3.12中的兼容性问题
"""

import subprocess
import sys
import os

def uninstall_scapy():
    """卸载当前的Scapy"""
    print("🔄 卸载当前Scapy版本...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "uninstall", "scapy", "-y"])
        print("✅ 成功卸载Scapy")
        return True
    except subprocess.CalledProcessError as e:
        print(f"⚠️  卸载Scapy时出现问题: {e}")
        return True  # 继续执行，可能本来就没安装

def install_compatible_scapy():
    """安装兼容的Scapy版本"""
    print("📦 安装兼容的Scapy版本...")
    
    # 尝试不同的Scapy版本
    scapy_versions = [
        "scapy>=2.5.0",  # 最新稳定版
        "scapy==2.4.5",  # 较新的稳定版
        "scapy"          # 默认最新版
    ]
    
    for version in scapy_versions:
        try:
            print(f"  尝试安装: {version}")
            subprocess.check_call([sys.executable, "-m", "pip", "install", version])
            print(f"✅ 成功安装: {version}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装失败: {version} - {e}")
            continue
    
    return False

def test_scapy_import():
    """测试Scapy导入"""
    print("🔍 测试Scapy导入...")
    
    try:
        import scapy.all as scapy
        print("✅ scapy.all 导入成功")
        
        # 测试基本功能
        print("🔍 测试基本功能...")
        # 这里不实际读取文件，只测试函数是否可用
        if hasattr(scapy, 'rdpcap'):
            print("✅ rdpcap 函数可用")
        if hasattr(scapy, 'wrpcap'):
            print("✅ wrpcap 函数可用")
        
        return True
        
    except ImportError as e:
        print(f"❌ Scapy导入失败: {e}")
        return False

def install_alternative_packages():
    """安装替代包"""
    print("📦 安装替代网络分析包...")
    
    alternative_packages = [
        "pyshark",  # Wireshark的Python接口
        "dpkt",     # 轻量级包解析器
    ]
    
    for package in alternative_packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ 成功安装替代包: {package}")
        except subprocess.CalledProcessError as e:
            print(f"⚠️  安装替代包失败: {package} - {e}")

def create_scapy_wrapper():
    """创建Scapy包装器以处理兼容性问题"""
    print("🔧 创建Scapy兼容性包装器...")
    
    wrapper_code = '''
"""
Scapy兼容性包装器
处理不同Python版本的兼容性问题
"""

import sys

# 尝试导入Scapy
try:
    from scapy.all import *
    SCAPY_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Scapy import failed: {e}")
    SCAPY_AVAILABLE = False
    
    # 创建基本的替代函数
    def rdpcap(filename):
        """Fallback rdpcap function"""
        print(f"Warning: Using fallback rdpcap for {filename}")
        return []
    
    def wrpcap(filename, packets):
        """Fallback wrpcap function"""
        print(f"Warning: Using fallback wrpcap for {filename}")
        pass
    
    class Packet:
        """Fallback Packet class"""
        def __init__(self):
            pass
        
        def copy(self):
            return self

def safe_rdpcap(filename):
    """安全的rdpcap函数"""
    if SCAPY_AVAILABLE:
        return rdpcap(filename)
    else:
        print(f"Warning: Scapy not available, cannot read {filename}")
        return []

def safe_wrpcap(filename, packets):
    """安全的wrpcap函数"""
    if SCAPY_AVAILABLE:
        return wrpcap(filename, packets)
    else:
        print(f"Warning: Scapy not available, cannot write {filename}")
'''
    
    try:
        with open("scapy_wrapper.py", "w") as f:
            f.write(wrapper_code)
        print("✅ 创建Scapy包装器成功")
        return True
    except Exception as e:
        print(f"❌ 创建Scapy包装器失败: {e}")
        return False

def main():
    print("🚀 Scapy兼容性修复脚本")
    print("="*50)
    
    # 步骤1: 卸载当前版本
    uninstall_scapy()
    
    # 步骤2: 安装兼容版本
    install_success = install_compatible_scapy()
    
    # 步骤3: 测试导入
    import_success = test_scapy_import()
    
    if not import_success:
        print("\n⚠️  Scapy仍然有问题，尝试其他解决方案...")
        
        # 步骤4: 安装替代包
        install_alternative_packages()
        
        # 步骤5: 创建包装器
        create_scapy_wrapper()
    
    print("\n" + "="*50)
    if import_success:
        print("🎉 Scapy修复成功!")
        print("现在可以运行ET-BERT了:")
        print("  python run_netmamba_dataset.py --step all")
    else:
        print("⚠️  Scapy问题未完全解决")
        print("建议手动解决方案:")
        print("1. 创建新的conda环境:")
        print("   conda create -n etbert python=3.9")
        print("   conda activate etbert")
        print("   pip install scapy==2.4.5")
        print("\n2. 或者使用Docker环境运行")
    
    return 0 if import_success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
