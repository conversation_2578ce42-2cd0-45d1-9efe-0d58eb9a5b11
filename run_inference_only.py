#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
仅运行推理，因为训练已经完成
"""

import os
import sys
import subprocess
import time

def check_model_exists():
    """检查微调模型是否存在"""
    model_path = "models/finetuned_model.bin"
    if os.path.exists(model_path):
        size_mb = os.path.getsize(model_path) / (1024*1024)
        print(f"✅ 微调模型存在: {size_mb:.2f} MB")
        return True
    else:
        print(f"❌ 微调模型不存在: {model_path}")
        return False

def run_inference_with_cpu():
    """使用CPU运行推理以避免GPU内存问题"""
    print("🔮 使用CPU运行推理（避免GPU内存问题）...")
    
    # 设置环境变量强制使用CPU
    env = os.environ.copy()
    env['CUDA_VISIBLE_DEVICES'] = ''
    
    infer_cmd = [
        sys.executable, "inference/run_classifier_infer.py",
        "--load_model_path", "models/finetuned_model.bin",
        "--vocab_path", "models/encryptd_vocab.txt",
        "--test_path", "/home/<USER>/ET-BERT/output/datasets/nolabel_test_dataset.tsv",
        "--prediction_path", "/home/<USER>/ET-BERT/output/prediction.tsv",
        "--labels_num", "18",
        "--embedding", "word_pos_seg",
        "--encoder", "transformer",
        "--mask", "fully_visible",
        "--seq_length", "128",
        "--batch_size", "8"  # 减少批次大小
    ]
    
    print("📝 推理命令:")
    print("  " + " ".join(infer_cmd))
    print("  环境: CUDA_VISIBLE_DEVICES='' (强制CPU)")
    print()
    
    try:
        start_time = time.time()
        
        process = subprocess.Popen(
            infer_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=0,
            env=env
        )
        
        line_count = 0
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            
            if output:
                line = output.strip()
                line_count += 1
                
                timestamp = time.strftime("%H:%M:%S")
                elapsed = (time.time() - start_time) / 60
                
                print(f"[{timestamp}] [{elapsed:6.1f}min] [{line_count:4d}] {line}")
                
                # 检查重要信息
                if any(keyword in line.lower() for keyword in ['error', 'exception', 'failed']):
                    print(f"    ❌ 错误: {line}")
                elif any(keyword in line.lower() for keyword in ['complete', 'finished', 'done']):
                    print(f"    ✅ 完成: {line}")
                
                sys.stdout.flush()
        
        return_code = process.poll()
        total_time = (time.time() - start_time) / 60
        
        print(f"\n📊 推理完成!")
        print(f"  返回码: {return_code}")
        print(f"  运行时间: {total_time:.1f} 分钟")
        print(f"  输出行数: {line_count}")
        
        if return_code == 0:
            print("✅ 推理成功!")
            
            # 检查预测文件
            pred_path = "/home/<USER>/ET-BERT/output/prediction.tsv"
            if os.path.exists(pred_path):
                file_size = os.path.getsize(pred_path)
                print(f"📊 预测文件已生成: {file_size} bytes")
                
                # 显示前几行
                print("\n📋 预测结果预览:")
                try:
                    with open(pred_path, 'r') as f:
                        for i, line in enumerate(f):
                            if i < 5:
                                print(f"  {line.strip()}")
                            else:
                                break
                except:
                    pass
                
                return True
            else:
                print(f"❌ 预测文件未生成")
                return False
        else:
            print(f"❌ 推理失败")
            return False
            
    except Exception as e:
        print(f"❌ 推理出错: {e}")
        return False

def calculate_training_metrics():
    """根据训练日志计算指标"""
    print("\n📈 训练指标总结:")
    print("=" * 50)
    
    # 从您提供的数据中提取的指标
    epochs_data = [
        (1, 2.922, 0.0650),  # (epoch, avg_loss, accuracy)
        (2, 2.904, 0.0701),
        (3, 2.898, 0.0621),
        (4, 2.898, 0.0547),
        (5, 2.895, 0.0621),
        (6, 2.887, 0.0615),
        (7, 2.879, 0.0581),
        (8, 2.898, 0.0598),
        (9, 2.884, 0.0530),
        (10, 2.884, 0.0610)
    ]
    
    print(f"{'Epoch':<8} {'Loss':<10} {'Accuracy':<10} {'Accuracy %':<12}")
    print("-" * 50)
    
    for epoch, loss, acc in epochs_data:
        print(f"{epoch:<8} {loss:<10.3f} {acc:<10.4f} {acc*100:<12.2f}%")
    
    # 计算改进
    initial_loss = epochs_data[0][1]
    final_loss = epochs_data[-1][1]
    loss_improvement = initial_loss - final_loss
    
    initial_acc = epochs_data[0][2]
    final_acc = epochs_data[-1][2]
    
    print("-" * 50)
    print(f"📊 训练总结:")
    print(f"  损失改进: {loss_improvement:.3f} ({initial_loss:.3f} → {final_loss:.3f})")
    print(f"  最终准确率: {final_acc*100:.2f}%")
    print(f"  最高准确率: {max(acc for _, _, acc in epochs_data)*100:.2f}%")

def main():
    print("🚀 ET-BERT 推理执行器")
    print("=" * 50)
    
    # 显示训练指标
    calculate_training_metrics()
    
    # 检查模型
    if not check_model_exists():
        print("❌ 微调模型不存在，无法进行推理")
        return 1
    
    # 运行推理
    if run_inference_with_cpu():
        print("\n🎉 推理完成! 现在可以分析结果:")
        print("  python analyze_results.py")
        return 0
    else:
        print("\n❌ 推理失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
