#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
重新生成改进的数据集
- 每个类别10000个样本
- 更严格的数据质量检查
- 跳过不符合要求的数据包
"""

import os
import shutil
import subprocess
import sys
import time

def clean_old_data():
    """清理旧的数据"""
    print("🗑️  清理旧数据...")
    
    files_to_remove = [
        "/home/<USER>/ET-BERT/output/result/dataset.json",
        "/home/<USER>/ET-BERT/output/result/dataset/",
        "/home/<USER>/ET-BERT/output/datasets/",
    ]
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    print(f"  ✅ 删除文件: {os.path.basename(file_path)}")
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                    print(f"  ✅ 删除目录: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"  ❌ 删除失败 {file_path}: {e}")

def show_improvement_summary():
    """显示改进总结"""
    print("📈 数据集改进总结:")
    print("=" * 60)
    print("🔧 改进内容:")
    print("  1. 样本数量: 1,000 → 10,000 (每个类别)")
    print("  2. 数据质量检查:")
    print("     - 跳过数据包太小的样本 (<100字符)")
    print("     - 跳过载荷太短的样本 (<40字符)")
    print("     - 跳过字符种类太少的样本 (<4种字符)")
    print("     - 跳过特征太短的样本 (<50字符)")
    print("  3. 训练轮数: 10 → 100 epochs")
    print("  4. 更详细的进度监控")
    print()
    print("🎯 预期效果:")
    print("  - 更高质量的训练数据")
    print("  - 更充分的模型训练")
    print("  - 显著提升分类准确率")
    print("=" * 60)

def run_improved_data_generation():
    """运行改进的数据生成"""
    print("\n🚀 开始改进的数据生成...")
    
    # 修改main.py中的参数
    print("⚙️  更新配置参数...")
    
    # 运行数据预处理
    cmd = [sys.executable, "data_process/main.py"]
    
    print("📝 数据生成命令:")
    print("  " + " ".join(cmd))
    print()
    
    try:
        start_time = time.time()
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=0
        )
        
        line_count = 0
        last_progress_time = time.time()
        
        print("📊 数据生成进度:")
        print("-" * 60)
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            
            if output:
                line = output.strip()
                line_count += 1
                
                timestamp = time.strftime("%H:%M:%S")
                elapsed = (time.time() - start_time) / 60
                
                print(f"[{timestamp}] [{elapsed:6.1f}min] [{line_count:4d}] {line}")
                
                # 检查重要信息
                if any(keyword in line.lower() for keyword in ['类别', '样本', '完成', 'epoch']):
                    print(f"    🎯 重要: {line}")
                elif any(keyword in line.lower() for keyword in ['error', 'failed', '失败']):
                    print(f"    ❌ 错误: {line}")
                
                # 每30秒显示一次心跳
                now = time.time()
                if (now - last_progress_time) > 30:
                    print(f"    💓 数据生成进行中... (已运行 {elapsed:.1f} 分钟)")
                    last_progress_time = now
                
                sys.stdout.flush()
        
        return_code = process.poll()
        total_time = (time.time() - start_time) / 60
        
        print(f"\n📊 数据生成完成!")
        print(f"  返回码: {return_code}")
        print(f"  运行时间: {total_time:.1f} 分钟")
        print(f"  输出行数: {line_count}")
        
        if return_code == 0:
            print("✅ 数据生成成功!")
            return True
        else:
            print("❌ 数据生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据生成出错: {e}")
        return False

def check_generated_data():
    """检查生成的数据"""
    print("\n🔍 检查生成的数据...")
    
    # 检查numpy文件
    numpy_files = [
        "/home/<USER>/ET-BERT/output/result/dataset/x_datagram_train.npy",
        "/home/<USER>/ET-BERT/output/result/dataset/y_train.npy"
    ]
    
    for file_path in numpy_files:
        if os.path.exists(file_path):
            size_mb = os.path.getsize(file_path) / (1024*1024)
            print(f"  ✅ {os.path.basename(file_path)}: {size_mb:.2f} MB")
        else:
            print(f"  ❌ {os.path.basename(file_path)}: 不存在")
    
    # 检查JSON文件
    json_file = "/home/<USER>/ET-BERT/output/result/dataset.json"
    if os.path.exists(json_file):
        try:
            import json
            with open(json_file, 'r') as f:
                dataset = json.load(f)
            
            print(f"\n📊 数据集统计:")
            total_samples = 0
            for label, data in dataset.items():
                sample_count = data.get('samples', 0)
                total_samples += sample_count
                print(f"  类别 {label}: {sample_count:,} 个样本")
            
            print(f"  总计: {total_samples:,} 个样本")
            
            if total_samples > 100000:  # 期望至少10万个样本
                print("✅ 样本数量充足!")
                return True
            else:
                print("⚠️  样本数量可能不足")
                return False
                
        except Exception as e:
            print(f"❌ 读取数据集统计失败: {e}")
            return False
    else:
        print(f"❌ 数据集JSON文件不存在")
        return False

def generate_tsv_files():
    """生成TSV文件"""
    print("\n📄 生成TSV训练文件...")
    
    try:
        result = subprocess.run([sys.executable, "generate_tsv_datasets.py"], 
                              capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ TSV文件生成成功")
            print(result.stdout)
            return True
        else:
            print("❌ TSV文件生成失败")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ TSV文件生成超时")
        return False
    except Exception as e:
        print(f"❌ TSV文件生成出错: {e}")
        return False

def main():
    print("🚀 改进数据集重新生成器")
    print("=" * 60)
    
    # 显示改进总结
    show_improvement_summary()
    
    # 确认是否继续
    response = input("\n是否继续重新生成数据集? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("👋 用户取消操作")
        return 0
    
    # 清理旧数据
    clean_old_data()
    
    # 运行改进的数据生成
    if not run_improved_data_generation():
        print("❌ 数据生成失败")
        return 1
    
    # 检查生成的数据
    if not check_generated_data():
        print("⚠️  数据质量检查未通过，但可以继续")
    
    # 生成TSV文件
    if not generate_tsv_files():
        print("❌ TSV文件生成失败")
        return 1
    
    print("\n🎉 改进数据集生成完成!")
    print("📋 下一步:")
    print("  1. 运行训练: python train_with_verbose_output.py")
    print("  2. 预期训练时间: 数小时 (100 epochs)")
    print("  3. 预期准确率提升: 显著改善")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
