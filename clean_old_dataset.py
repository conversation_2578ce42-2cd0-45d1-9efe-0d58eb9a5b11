#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
清理旧的数据集文件，强制重新生成
"""

import os
import shutil

def clean_old_files():
    """清理旧的数据集文件"""
    print("🗑️  清理旧的数据集文件...")
    
    files_to_remove = [
        "/home/<USER>/ET-BERT/output/result/dataset.json",
        "/home/<USER>/ET-BERT/output/result/dataset/",
        "/home/<USER>/ET-BERT/output/datasets/",
    ]
    
    removed_count = 0
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    print(f"  ✅ 删除文件: {file_path}")
                    removed_count += 1
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                    print(f"  ✅ 删除目录: {file_path}")
                    removed_count += 1
            except Exception as e:
                print(f"  ❌ 删除失败 {file_path}: {e}")
        else:
            print(f"  ℹ️  文件不存在: {file_path}")
    
    if removed_count > 0:
        print(f"✅ 清理完成，删除了 {removed_count} 个文件/目录")
    else:
        print("ℹ️  没有找到需要清理的文件")
    
    return True

def main():
    print("🚀 清理旧数据集文件")
    print("="*50)
    
    clean_old_files()
    
    print("\n现在可以重新运行数据处理:")
    print("  python run_netmamba_dataset.py --step preprocess")
    print("或者:")
    print("  python run_netmamba_dataset.py --step all --force_regenerate")

if __name__ == "__main__":
    main()
