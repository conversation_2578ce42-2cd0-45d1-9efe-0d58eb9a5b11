#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
ET-BERT Custom Dataset Adapter
适配自定义PCAP数据集到ET-BERT模型的脚本

使用方法:
1. 将您的PCAP文件按类别组织到不同文件夹中
2. 修改下面的配置参数
3. 运行脚本进行数据预处理
"""

import os
import sys
import json
import shutil
import random
import argparse
from pathlib import Path

# 添加项目路径到系统路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入项目模块
import data_process.dataset_generation as dataset_generation
import data_process.open_dataset_deal as open_dataset_deal

class CustomDatasetAdapter:
    def __init__(self, config):
        """
        初始化适配器
        
        Args:
            config (dict): 配置参数字典
        """
        self.config = config
        self.validate_config()
        self.setup_directories()
    
    def validate_config(self):
        """验证配置参数"""
        required_keys = ['pcap_input_path', 'output_path', 'num_classes', 'samples_per_class']
        for key in required_keys:
            if key not in self.config:
                raise ValueError(f"配置中缺少必需参数: {key}")
        
        if not os.path.exists(self.config['pcap_input_path']):
            raise ValueError(f"输入路径不存在: {self.config['pcap_input_path']}")
    
    def setup_directories(self):
        """设置输出目录结构"""
        self.output_path = Path(self.config['output_path'])
        self.splitcap_path = self.output_path / "splitcap"
        self.dataset_path = self.output_path / "dataset"
        self.tsv_path = self.output_path / "tsv_files"
        
        # 创建必要的目录
        for path in [self.output_path, self.splitcap_path, self.dataset_path, self.tsv_path]:
            path.mkdir(parents=True, exist_ok=True)
    
    def organize_pcap_files(self):
        """
        组织PCAP文件结构
        确保每个类别的PCAP文件都在单独的文件夹中
        """
        print("正在组织PCAP文件结构...")
        input_path = Path(self.config['pcap_input_path'])
        
        # 检查输入路径结构
        subdirs = [d for d in input_path.iterdir() if d.is_dir()]
        
        if not subdirs:
            # 如果没有子目录，假设所有文件都是一个类别
            print("检测到单一类别数据集，创建默认类别文件夹...")
            default_class_dir = input_path / "class_0"
            default_class_dir.mkdir(exist_ok=True)
            
            # 移动所有pcap文件到默认类别文件夹
            for file in input_path.glob("*.pcap"):
                shutil.move(str(file), str(default_class_dir / file.name))
            for file in input_path.glob("*.pcapng"):
                shutil.move(str(file), str(default_class_dir / file.name))
        
        # 统计每个类别的文件数量
        class_stats = {}
        for class_dir in input_path.iterdir():
            if class_dir.is_dir():
                pcap_files = list(class_dir.glob("*.pcap")) + list(class_dir.glob("*.pcapng"))
                class_stats[class_dir.name] = len(pcap_files)
                print(f"类别 {class_dir.name}: {len(pcap_files)} 个文件")
        
        return class_stats
    
    def convert_pcapng_to_pcap(self):
        """将pcapng文件转换为pcap格式"""
        print("正在转换pcapng文件为pcap格式...")
        input_path = Path(self.config['pcap_input_path'])
        
        for class_dir in input_path.iterdir():
            if not class_dir.is_dir():
                continue
                
            for pcapng_file in class_dir.glob("*.pcapng"):
                pcap_file = pcapng_file.with_suffix('.pcap')
                try:
                    # 使用tshark转换文件格式
                    cmd = f'tshark -F pcap -r "{pcapng_file}" -w "{pcap_file}"'
                    result = os.system(cmd)
                    if result == 0:
                        print(f"转换成功: {pcapng_file.name} -> {pcap_file.name}")
                        # 删除原始pcapng文件
                        pcapng_file.unlink()
                    else:
                        print(f"转换失败: {pcapng_file.name}")
                except Exception as e:
                    print(f"转换文件时出错 {pcapng_file.name}: {e}")
    
    def split_pcap_files(self):
        """
        使用SplitCap将PCAP文件分割为会话
        """
        print("正在分割PCAP文件为会话...")
        input_path = Path(self.config['pcap_input_path'])
        dataset_level = self.config.get('dataset_level', 'packet')
        
        for class_dir in input_path.iterdir():
            if not class_dir.is_dir():
                continue
            
            class_name = class_dir.name
            print(f"处理类别: {class_name}")
            
            # 为每个类别创建输出目录
            class_output_dir = self.splitcap_path / class_name
            class_output_dir.mkdir(exist_ok=True)
            
            for pcap_file in class_dir.glob("*.pcap"):
                try:
                    # 使用SplitCap分割文件
                    if dataset_level == 'flow':
                        cmd = f'SplitCap.exe -r "{pcap_file}" -s session -o "{class_output_dir}"'
                    else:  # packet level
                        cmd = f'SplitCap.exe -r "{pcap_file}" -s packets 1 -o "{class_output_dir}"'
                    
                    result = os.system(cmd)
                    if result == 0:
                        print(f"分割成功: {pcap_file.name}")
                    else:
                        print(f"分割失败: {pcap_file.name}")
                except Exception as e:
                    print(f"分割文件时出错 {pcap_file.name}: {e}")
    
    def clean_small_files(self):
        """清理过小的文件"""
        print("正在清理过小的文件...")
        min_size_kb = self.config.get('min_file_size_kb', 1)
        min_size_bytes = min_size_kb * 1024
        
        removed_count = 0
        for pcap_file in self.splitcap_path.rglob("*.pcap"):
            if pcap_file.stat().st_size < min_size_bytes:
                pcap_file.unlink()
                removed_count += 1
        
        print(f"删除了 {removed_count} 个小于 {min_size_kb}KB 的文件")
    
    def generate_dataset_features(self):
        """生成数据集特征"""
        print("正在生成数据集特征...")
        
        # 设置参数
        samples = [self.config['samples_per_class']] * self.config['num_classes']
        features = self.config.get('features', ['payload'])
        dataset_level = self.config.get('dataset_level', 'packet')
        payload_length = self.config.get('payload_length', 128)
        
        # 调用数据生成函数
        try:
            X, Y = dataset_generation.generation(
                pcap_path=str(self.splitcap_path) + "/",
                samples=samples,
                features=features,
                splitcap=False,
                payload_length=payload_length,
                dataset_save_path=str(self.output_path) + "/",
                dataset_level=dataset_level
            )
            
            print("特征生成完成!")
            return X, Y
        except Exception as e:
            print(f"生成特征时出错: {e}")
            return None, None
    
    def convert_to_tsv_format(self):
        """将数据转换为TSV格式供ET-BERT使用"""
        print("正在转换数据为TSV格式...")
        
        # 读取生成的数据集
        dataset_json_path = self.output_path / "dataset.json"
        if not dataset_json_path.exists():
            print("错误: 未找到dataset.json文件")
            return False
        
        with open(dataset_json_path, 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        
        # 准备训练、验证和测试数据
        all_data = []
        all_labels = []
        
        for label_id, label_data in dataset.items():
            samples = label_data['samples']
            payload_data = label_data['payload']
            
            for sample_id, payload in payload_data.items():
                all_data.append(payload)
                all_labels.append(int(label_id))
        
        # 随机打乱数据
        combined = list(zip(all_data, all_labels))
        random.shuffle(combined)
        all_data, all_labels = zip(*combined)
        
        # 分割数据集 (70% 训练, 15% 验证, 15% 测试)
        total_samples = len(all_data)
        train_end = int(0.7 * total_samples)
        valid_end = int(0.85 * total_samples)
        
        train_data = all_data[:train_end]
        train_labels = all_labels[:train_end]
        
        valid_data = all_data[train_end:valid_end]
        valid_labels = all_labels[train_end:valid_end]
        
        test_data = all_data[valid_end:]
        test_labels = all_labels[valid_end:]
        
        # 保存TSV文件
        self._save_tsv_file(train_data, train_labels, self.tsv_path / "train_dataset.tsv")
        self._save_tsv_file(valid_data, valid_labels, self.tsv_path / "valid_dataset.tsv")
        self._save_tsv_file(test_data, test_labels, self.tsv_path / "test_dataset.tsv")
        
        # 创建无标签测试文件用于推理
        self._save_nolabel_tsv_file(test_data, self.tsv_path / "nolabel_test_dataset.tsv")
        
        print(f"TSV文件已保存到: {self.tsv_path}")
        print(f"训练集: {len(train_data)} 样本")
        print(f"验证集: {len(valid_data)} 样本")
        print(f"测试集: {len(test_data)} 样本")
        
        return True
    
    def _save_tsv_file(self, data, labels, filepath):
        """保存TSV格式文件"""
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write("label\ttext_a\n")
            for label, text in zip(labels, data):
                f.write(f"{label}\t{text}\n")
    
    def _save_nolabel_tsv_file(self, data, filepath):
        """保存无标签TSV文件"""
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write("text_a\n")
            for text in data:
                f.write(f"{text}\n")
    
    def run_full_pipeline(self):
        """运行完整的数据处理流水线"""
        print("开始运行完整的数据处理流水线...")
        
        try:
            # 1. 组织文件结构
            class_stats = self.organize_pcap_files()
            
            # 2. 转换文件格式
            self.convert_pcapng_to_pcap()
            
            # 3. 分割PCAP文件
            self.split_pcap_files()
            
            # 4. 清理小文件
            self.clean_small_files()
            
            # 5. 生成特征
            X, Y = self.generate_dataset_features()
            
            if X is not None and Y is not None:
                # 6. 转换为TSV格式
                success = self.convert_to_tsv_format()
                
                if success:
                    print("\n数据处理完成!")
                    print(f"输出目录: {self.output_path}")
                    print(f"TSV文件位置: {self.tsv_path}")
                    print("\n下一步可以使用这些TSV文件进行模型训练:")
                    print(f"  训练文件: {self.tsv_path}/train_dataset.tsv")
                    print(f"  验证文件: {self.tsv_path}/valid_dataset.tsv")
                    print(f"  测试文件: {self.tsv_path}/test_dataset.tsv")
                else:
                    print("TSV转换失败")
            else:
                print("特征生成失败")
                
        except Exception as e:
            print(f"处理过程中出错: {e}")
            import traceback
            traceback.print_exc()


def main():
    parser = argparse.ArgumentParser(description='ET-BERT自定义数据集适配器')
    parser.add_argument('--input_path', type=str, required=True, 
                       help='输入PCAP文件路径')
    parser.add_argument('--output_path', type=str, required=True,
                       help='输出路径')
    parser.add_argument('--num_classes', type=int, required=True,
                       help='数据集类别数量')
    parser.add_argument('--samples_per_class', type=int, default=1000,
                       help='每个类别的样本数量')
    parser.add_argument('--dataset_level', choices=['packet', 'flow'], default='packet',
                       help='数据集级别: packet或flow')
    parser.add_argument('--payload_length', type=int, default=128,
                       help='载荷长度')
    parser.add_argument('--min_file_size_kb', type=float, default=1.0,
                       help='最小文件大小(KB)')
    
    args = parser.parse_args()
    
    # 构建配置
    config = {
        'pcap_input_path': args.input_path,
        'output_path': args.output_path,
        'num_classes': args.num_classes,
        'samples_per_class': args.samples_per_class,
        'dataset_level': args.dataset_level,
        'payload_length': args.payload_length,
        'min_file_size_kb': args.min_file_size_kb,
        'features': ['payload']
    }
    
    # 创建适配器并运行
    adapter = CustomDatasetAdapter(config)
    adapter.run_full_pipeline()


if __name__ == "__main__":
    main()
