#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
简化的dataset_generation.py
专门为ET-BERT项目优化，增加详细的进度输出
"""

import os
import json
import tqdm
import random
import binascii
import numpy as np
import time

# 兼容Scapy导入
try:
    import scapy.all as scapy
    SCAPY_AVAILABLE = True
    print("✅ Scapy导入成功")
except ImportError:
    print("⚠️  Scapy不可用，使用模拟模式")
    SCAPY_AVAILABLE = False
    
    class MockScapy:
        @staticmethod
        def rdpcap(filename):
            print(f"📦 模拟读取PCAP文件: {os.path.basename(filename)}")
            # 返回模拟的数据包列表
            return [type('MockPacket', (), {'copy': lambda: type('MockData', (), {})()})() for _ in range(5)]
    scapy = MockScapy()

# 设置路径
word_dir = "/home/<USER>/ET-BERT/output/corpora/"
word_name = "encrypted_burst.txt"

print(f"📁 语料库目录: {word_dir}")
print(f"📄 语料库文件: {word_name}")

random.seed(40)

def cut(obj, sec):
    """分割字符串"""
    result = [obj[i:i+sec] for i in range(0,len(obj),sec)]
    try:
        remanent_count = len(result[0])%4
    except:
        remanent_count = 0
    if remanent_count == 0:
        pass
    else:
        result = [obj[i:i+sec+remanent_count] for i in range(0,len(obj),sec+remanent_count)]
    return result

def bigram_generation(packet_datagram, packet_len = 64, flag=True):
    """生成bigram特征"""
    result = ''
    generated_datagram = cut(packet_datagram,1)
    token_count = 0
    for sub_string_index in range(len(generated_datagram)):
        if sub_string_index != (len(generated_datagram) - 1):
            token_count += 1
            if token_count > packet_len:
                break
            else:
                merge_word_bigram = generated_datagram[sub_string_index] + generated_datagram[sub_string_index + 1]
        else:
            break
        result += merge_word_bigram
        result += ' '
    
    return result

def get_feature_packet(label_pcap, payload_len, max_samples_per_file=5000):
    """提取包级别特征 - 每个数据包作为一个样本"""
    print(f"  📦 处理文件: {os.path.basename(label_pcap)}")
    feature_data = []

    if not SCAPY_AVAILABLE:
        # 模拟数据 - 生成多个样本
        print(f"    ⚠️  Scapy不可用，生成 {max_samples_per_file} 个模拟样本")
        for i in range(max_samples_per_file):
            mock_data = "48656c6c6f20576f726c64" * (10 + i % 5)  # 变化的模拟数据
            packet_data_string = bigram_generation(mock_data, packet_len=payload_len, flag=True)
            feature_data.append(packet_data_string)
        return feature_data

    try:
        packets = scapy.rdpcap(label_pcap)
        total_packets = len(packets)
        print(f"    📊 读取到 {total_packets} 个数据包")

        # 如果数据包太多，随机采样
        if total_packets > max_samples_per_file:
            selected_indices = random.sample(range(total_packets), max_samples_per_file)
            selected_packets = [packets[i] for i in selected_indices]
            print(f"    🎲 随机选择 {max_samples_per_file} 个数据包")
        else:
            selected_packets = packets
            print(f"    📋 使用全部 {total_packets} 个数据包")

        # 为每个数据包生成一个样本，增加质量检查
        valid_samples = 0
        for i, packet in enumerate(selected_packets):
            if i % 500 == 0 and i > 0:
                print(f"    🔄 已处理 {i}/{len(selected_packets)} 个数据包，有效样本: {valid_samples}")

            try:
                packet_data = packet.copy()
                data = (binascii.hexlify(bytes(packet_data)))
                packet_string = data.decode()

                # 数据质量检查
                if len(packet_string) < 100:  # 数据包太小，跳过
                    continue

                # 提取载荷部分（跳过以太网头、IP头、TCP/UDP头）
                new_packet_string = packet_string[76:] if len(packet_string) > 76 else packet_string

                # 更严格的载荷检查
                if len(new_packet_string) < 40:  # 至少20个字节的载荷
                    # 如果载荷太短，尝试使用更多数据
                    new_packet_string = packet_string[40:] if len(packet_string) > 40 else packet_string
                    if len(new_packet_string) < 40:
                        continue  # 仍然太短，跳过

                # 检查是否包含有效的载荷数据（不全是0或重复字符）
                if len(set(new_packet_string)) < 4:  # 字符种类太少，可能是填充数据
                    continue

                packet_feature = bigram_generation(new_packet_string, packet_len=payload_len, flag=True)

                # 更严格的特征检查
                if packet_feature.strip() and len(packet_feature.strip()) > 50:  # 确保特征足够长
                    feature_data.append(packet_feature)
                    valid_samples += 1

            except Exception as e:
                print(f"    ⚠️  处理第 {i} 个数据包时出错: {e}")
                continue

        print(f"    ✅ 特征提取完成，生成 {len(feature_data)} 个样本")

    except Exception as e:
        print(f"    ❌ 处理PCAP文件出错: {e}")
        # 返回模拟数据
        print(f"    ⚠️  生成 {max_samples_per_file} 个模拟样本作为替代")
        for i in range(max_samples_per_file):
            mock_data = "48656c6c6f20576f726c64" * (10 + i % 5)
            packet_data_string = bigram_generation(mock_data, packet_len=payload_len, flag=True)
            feature_data.append(packet_data_string)

    return feature_data

def generation(pcap_path, samples, features, splitcap=False, payload_length=128, payload_packet=5, dataset_save_path="", dataset_level="packet"):
    """主要的数据生成函数"""
    print(f"🚀 开始数据生成流程")
    print(f"📁 输入路径: {pcap_path}")
    print(f"📊 样本配置: {samples}")
    print(f"🎯 特征类型: {features}")
    print(f"📂 输出路径: {dataset_save_path}")
    print(f"🔧 数据级别: {dataset_level}")
    print(f"📏 载荷长度: {payload_length}")
    
    # 检查是否已有处理结果
    dataset_json_path = os.path.join(dataset_save_path, "dataset.json")
    if os.path.exists(dataset_json_path):
        print("✅ 发现已有数据集文件，直接加载...")
        return obtain_data(pcap_path, samples, features, dataset_save_path)
    
    print("🔍 扫描数据集结构...")
    dataset = {}
    label_name_list = []
    
    # 扫描类别文件夹
    for item in os.listdir(pcap_path):
        item_path = os.path.join(pcap_path, item)
        if os.path.isdir(item_path):
            label_name_list.append(item)
    
    print(f"📋 发现 {len(label_name_list)} 个类别: {label_name_list}")
    
    # 为每个类别分配ID
    label_id = {}
    for index, label_name in enumerate(label_name_list):
        label_id[label_name] = index
        print(f"  📌 {label_name} -> ID: {index}")
    
    # 处理每个类别
    total_processed = 0
    start_time = time.time()
    
    for class_name in tqdm.tqdm(label_name_list, desc="处理类别"):
        print(f"\n🔄 处理类别: {class_name}")
        class_path = os.path.join(pcap_path, class_name)
        
        # 初始化数据结构
        if label_id[class_name] not in dataset:
            dataset[label_id[class_name]] = {
                "samples": 0,
                "payload": {}
            }
        
        # 收集该类别下的所有PCAP文件
        pcap_files = []
        for root, dirs, files in os.walk(class_path):
            for file in files:
                if file.endswith(('.pcap', '.pcapng')):
                    pcap_files.append(os.path.join(root, file))
        
        print(f"  📁 类别 {class_name}: 发现 {len(pcap_files)} 个PCAP文件")
        
        # 随机选择样本
        target_samples = samples[0] if len(samples) > 0 else 1000
        if len(pcap_files) > target_samples:
            selected_files = random.sample(pcap_files, target_samples)
            print(f"  🎲 随机选择 {target_samples} 个文件")
        else:
            selected_files = pcap_files
            print(f"  📋 使用全部 {len(selected_files)} 个文件")
        
        # 处理选中的文件
        processed_count = 0
        for i, pcap_file in enumerate(selected_files):
            try:
                print(f"  🔄 处理文件 {i+1}/{len(selected_files)}: {os.path.basename(pcap_file)}")

                # 计算每个文件应该生成多少样本
                samples_per_file = target_samples // len(selected_files) if len(selected_files) > 0 else target_samples
                if samples_per_file < 100:  # 至少每个文件100个样本
                    samples_per_file = min(1000, target_samples)

                print(f"    🎯 目标样本数: {samples_per_file}")

                if dataset_level == "packet":
                    feature_data_list = get_feature_packet(pcap_file, payload_length, samples_per_file)
                else:
                    # 流级别处理（简化版）
                    feature_data_list = get_feature_packet(pcap_file, payload_length, samples_per_file)

                # 添加所有生成的样本
                if feature_data_list:
                    for feature_data in feature_data_list:
                        if feature_data and feature_data.strip():
                            dataset[label_id[class_name]]["samples"] += 1
                            sample_id = str(dataset[label_id[class_name]]["samples"])
                            dataset[label_id[class_name]]["payload"][sample_id] = feature_data
                            processed_count += 1
                            total_processed += 1

                    print(f"    ✅ 从文件生成了 {len(feature_data_list)} 个样本")

                # 每处理1个文件显示一次进度
                elapsed = time.time() - start_time
                print(f"    ⏱️  已处理 {i+1}/{len(selected_files)} 个文件，耗时 {elapsed:.1f}s")

                # 如果已经达到目标样本数，提前结束
                if processed_count >= target_samples:
                    print(f"    🎯 已达到目标样本数 {target_samples}，停止处理")
                    break

            except Exception as e:
                print(f"    ❌ 处理文件 {pcap_file} 时出错: {e}")
                continue
        
        print(f"  ✅ 类别 {class_name} 处理完成: {processed_count} 个有效样本")
    
    # 保存结果
    print(f"\n💾 保存数据集到: {dataset_json_path}")
    os.makedirs(dataset_save_path, exist_ok=True)
    with open(dataset_json_path, "w") as f:
        json.dump(dataset, fp=f, ensure_ascii=False, indent=4)
    
    # 统计信息
    total_time = time.time() - start_time
    print(f"\n📊 数据处理完成统计:")
    print(f"  ⏱️  总耗时: {total_time:.1f}s")
    print(f"  📦 总样本数: {total_processed}")
    print(f"  📋 类别统计:")
    
    for label_name in label_name_list:
        sample_count = dataset[label_id[label_name]]["samples"]
        print(f"    {label_name}: {sample_count} 个样本")
    
    return obtain_data(pcap_path, samples, features, dataset_save_path, json_data=dataset)

def obtain_data(pcap_path, samples, features, dataset_save_path, json_data=None):
    """获取处理后的数据"""
    print(f"📤 准备输出数据...")
    
    if json_data:
        dataset = json_data
    else:
        dataset_json_path = os.path.join(dataset_save_path, "dataset.json")
        print(f"📖 从文件加载数据集: {dataset_json_path}")
        with open(dataset_json_path, "r") as f:
            dataset = json.load(f)
    
    X, Y = [], []
    
    print(f"🔄 处理特征: {features}")
    for feature_index in range(len(features)):
        x = []
        if not Y:  # 只在第一次生成标签
            y = []
            for label in dataset.keys():
                sample_num = dataset[label]["samples"]
                y.extend([int(label)] * sample_num)
                print(f"  📊 标签 {label}: {sample_num} 个样本")
            Y.append(y)
        
        feature_name = features[feature_index]
        print(f"  🎯 提取特征: {feature_name}")
        
        for label in dataset.keys():
            x_label = []
            if feature_name in dataset[label]:
                for sample_index in dataset[label][feature_name].keys():
                    x_label.append(dataset[label][feature_name][sample_index])
            x.extend(x_label)
        X.append(x)
    
    print(f"✅ 数据准备完成:")
    print(f"  📊 特征数组: {len(X)} 个")
    print(f"  🏷️  标签数组: {len(Y)} 个")
    if X and len(X[0]) > 0:
        print(f"  📦 总样本数: {len(X[0])}")
    
    return X, Y

def size_format(size):
    """格式化文件大小"""
    file_size = '%.3f' % float(size/1000)
    return file_size
