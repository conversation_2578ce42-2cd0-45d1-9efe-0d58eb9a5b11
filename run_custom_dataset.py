#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
ET-BERT 自定义数据集处理和训练脚本
一键运行完整的数据处理和模型训练流程
"""

import os
import sys
import argparse
from pathlib import Path

# 导入配置和适配器
from config_template import get_config, validate_config, print_config
from custom_dataset_adapter import CustomDatasetAdapter

def run_data_preprocessing(config):
    """
    运行数据预处理
    """
    print("=" * 60)
    print("步骤 1: 数据预处理")
    print("=" * 60)
    
    adapter = CustomDatasetAdapter(config)
    adapter.run_full_pipeline()
    
    return True

def run_model_training(config):
    """
    运行模型训练
    """
    print("=" * 60)
    print("步骤 2: 模型微调训练")
    print("=" * 60)
    
    # 构建训练命令
    tsv_path = Path(config['output_path']) / "tsv_files"
    
    train_cmd = f"""python fine-tuning/run_classifier.py \
        --pretrained_model_path {config['pretrained_model_path']} \
        --vocab_path {config['vocab_path']} \
        --train_path {tsv_path}/train_dataset.tsv \
        --dev_path {tsv_path}/valid_dataset.tsv \
        --test_path {tsv_path}/test_dataset.tsv \
        --epochs_num {config['epochs']} \
        --batch_size {config['batch_size']} \
        --embedding word_pos_seg \
        --encoder transformer \
        --mask fully_visible \
        --seq_length {config['sequence_length']} \
        --learning_rate {config['learning_rate']} \
        --labels_num {config['num_classes']}"""
    
    print("执行训练命令:")
    print(train_cmd)
    print()
    
    # 执行训练
    result = os.system(train_cmd)
    
    if result == 0:
        print("模型训练完成!")
        return True
    else:
        print("模型训练失败!")
        return False

def run_model_inference(config):
    """
    运行模型推理
    """
    print("=" * 60)
    print("步骤 3: 模型推理测试")
    print("=" * 60)
    
    # 构建推理命令
    tsv_path = Path(config['output_path']) / "tsv_files"
    output_path = Path(config['output_path'])
    
    infer_cmd = f"""python inference/run_classifier_infer.py \
        --load_model_path models/finetuned_model.bin \
        --vocab_path {config['vocab_path']} \
        --test_path {tsv_path}/nolabel_test_dataset.tsv \
        --prediction_path {output_path}/prediction.tsv \
        --labels_num {config['num_classes']} \
        --embedding word_pos_seg \
        --encoder transformer \
        --mask fully_visible \
        --seq_length {config['sequence_length']} \
        --batch_size {config['batch_size']}"""
    
    print("执行推理命令:")
    print(infer_cmd)
    print()
    
    # 执行推理
    result = os.system(infer_cmd)
    
    if result == 0:
        print("模型推理完成!")
        print(f"预测结果保存在: {output_path}/prediction.tsv")
        return True
    else:
        print("模型推理失败!")
        return False

def check_dependencies():
    """
    检查依赖工具是否可用
    """
    print("检查依赖工具...")
    
    dependencies = {
        'tshark': 'tshark --version',
        'SplitCap': 'SplitCap.exe',  # Windows工具，可能需要完整路径
    }
    
    missing_deps = []
    
    for tool, cmd in dependencies.items():
        try:
            result = os.system(f"{cmd} > nul 2>&1")  # Windows
            if result != 0:
                missing_deps.append(tool)
        except:
            missing_deps.append(tool)
    
    if missing_deps:
        print("警告: 以下工具未找到或不可用:")
        for tool in missing_deps:
            print(f"  - {tool}")
        print("\n请确保这些工具已安装并在系统PATH中，或在配置文件中指定完整路径。")
        return False
    
    print("所有依赖工具检查通过!")
    return True

def main():
    parser = argparse.ArgumentParser(description='ET-BERT自定义数据集处理和训练')
    parser.add_argument('--config_file', type=str, 
                       help='配置文件路径 (可选，默认使用config_template.py中的配置)')
    parser.add_argument('--step', choices=['preprocess', 'train', 'inference', 'all'], 
                       default='all', help='要执行的步骤')
    parser.add_argument('--skip_deps_check', action='store_true',
                       help='跳过依赖检查')
    
    # 数据集参数 (可以覆盖配置文件中的设置)
    parser.add_argument('--input_path', type=str, help='PCAP输入路径')
    parser.add_argument('--output_path', type=str, help='输出路径')
    parser.add_argument('--num_classes', type=int, help='类别数量')
    parser.add_argument('--samples_per_class', type=int, help='每类样本数量')
    
    args = parser.parse_args()
    
    # 获取配置
    if args.config_file and os.path.exists(args.config_file):
        # 如果指定了配置文件，导入它
        import importlib.util
        spec = importlib.util.spec_from_file_location("custom_config", args.config_file)
        custom_config = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(custom_config)
        config = custom_config.get_config()
    else:
        # 使用默认配置
        config = get_config()
    
    # 命令行参数覆盖配置文件设置
    if args.input_path:
        config['pcap_input_path'] = args.input_path
    if args.output_path:
        config['output_path'] = args.output_path
    if args.num_classes:
        config['num_classes'] = args.num_classes
    if args.samples_per_class:
        config['samples_per_class'] = args.samples_per_class
    
    # 验证配置
    if not validate_config(config):
        print("配置验证失败，请检查配置文件。")
        return 1
    
    # 打印配置
    print_config(config)
    
    # 检查依赖 (除非跳过)
    if not args.skip_deps_check:
        if not check_dependencies():
            print("依赖检查失败。如果您确定工具已正确安装，可以使用 --skip_deps_check 跳过此检查。")
            return 1
    
    # 执行指定步骤
    try:
        if args.step in ['preprocess', 'all']:
            if not run_data_preprocessing(config):
                print("数据预处理失败!")
                return 1
        
        if args.step in ['train', 'all']:
            if not run_model_training(config):
                print("模型训练失败!")
                return 1
        
        if args.step in ['inference', 'all']:
            if not run_model_inference(config):
                print("模型推理失败!")
                return 1
        
        print("\n" + "=" * 60)
        print("所有步骤完成!")
        print("=" * 60)
        
        if args.step == 'all':
            print(f"输出目录: {config['output_path']}")
            print(f"训练数据: {config['output_path']}/tsv_files/")
            print(f"预测结果: {config['output_path']}/prediction.tsv")
            print(f"微调模型: models/finetuned_model.bin")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        return 1
    except Exception as e:
        print(f"\n执行过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
