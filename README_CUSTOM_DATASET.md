# ET-BERT 自定义数据集适配工具包

本工具包帮助您将自己的PCAP数据集适配到ET-BERT模型中进行流量分类。

## 📦 工具包内容

### 核心脚本
- **`custom_dataset_adapter.py`** - 数据集适配器，处理PCAP文件并生成训练数据
- **`run_custom_dataset.py`** - 一键运行脚本，包含完整的数据处理和训练流程
- **`config_template.py`** - 配置文件模板，包含所有可调参数
- **`test_setup.py`** - 环境测试脚本，验证配置是否正确

### 文档
- **`CUSTOM_DATASET_GUIDE.md`** - 详细使用指南
- **`README_CUSTOM_DATASET.md`** - 本文件，快速开始指南

## 🚀 快速开始

### 1. 环境测试
首先运行环境测试，确保所有依赖都已正确安装：

```bash
python test_setup.py
```

### 2. 准备数据
将您的PCAP文件按类别组织到不同文件夹中：

```
your_data/
├── class1/
│   ├── sample1.pcap
│   └── sample2.pcap
├── class2/
│   ├── sample1.pcap
│   └── sample2.pcap
└── ...
```

### 3. 配置参数
编辑 `config_template.py`，修改以下关键参数：

```python
PCAP_INPUT_PATH = "D:/your_data/"     # 您的数据路径
OUTPUT_PATH = "D:/output/"            # 输出路径
NUM_CLASSES = 5                       # 类别数量
SAMPLES_PER_CLASS = 1000             # 每类样本数
```

### 4. 运行处理
执行完整的数据处理和训练流程：

```bash
python run_custom_dataset.py --step all
```

或者分步执行：

```bash
# 仅数据预处理
python run_custom_dataset.py --step preprocess

# 仅模型训练
python run_custom_dataset.py --step train

# 仅模型推理
python run_custom_dataset.py --step inference
```

## 📊 输出结果

运行完成后，您将得到：

- **训练数据**: `output/tsv_files/train_dataset.tsv`
- **验证数据**: `output/tsv_files/valid_dataset.tsv`
- **测试数据**: `output/tsv_files/test_dataset.tsv`
- **微调模型**: `models/finetuned_model.bin`
- **预测结果**: `output/prediction.tsv`

## 🛠️ 高级用法

### 命令行参数
```bash
python run_custom_dataset.py \
    --input_path "/path/to/pcaps/" \
    --output_path "/path/to/output/" \
    --num_classes 10 \
    --samples_per_class 500 \
    --step all
```

### 自定义配置文件
```bash
# 创建自定义配置
cp config_template.py my_config.py
# 编辑配置文件...

# 使用自定义配置
python run_custom_dataset.py --config_file my_config.py
```

### 仅使用适配器
```bash
python custom_dataset_adapter.py \
    --input_path "/path/to/pcaps/" \
    --output_path "/path/to/output/" \
    --num_classes 5 \
    --samples_per_class 1000 \
    --dataset_level packet
```

## 🔧 配置参数说明

### 基本参数
- `PCAP_INPUT_PATH`: PCAP文件输入路径
- `OUTPUT_PATH`: 处理结果输出路径
- `NUM_CLASSES`: 数据集类别数量
- `SAMPLES_PER_CLASS`: 每个类别的样本数量

### 数据处理参数
- `DATASET_LEVEL`: 数据级别 ('packet' 或 'flow')
- `PAYLOAD_LENGTH`: 载荷长度 (默认128)
- `MIN_FILE_SIZE_KB`: 最小文件大小 (默认1KB)

### 训练参数
- `BATCH_SIZE`: 批次大小 (默认32)
- `EPOCHS`: 训练轮数 (默认10)
- `LEARNING_RATE`: 学习率 (默认2e-5)
- `SEQUENCE_LENGTH`: 序列长度 (默认128)

## 📋 依赖要求

### Python包
```bash
pip install torch numpy scapy scikit-learn pandas tqdm
```

### 外部工具
- **tshark** (Wireshark的命令行工具)
- **SplitCap** (Windows) 或等效的PCAP分割工具

### 模型文件
- 预训练模型: `models/pretrained_model.bin`
- 词汇表: `models/encryptd_vocab.txt`
- 配置文件: `models/bert_base_config.json`

## ❓ 常见问题

### Q: 如何处理大型数据集？
A: 可以通过以下方式优化：
- 减少 `SAMPLES_PER_CLASS`
- 使用 `DATASET_LEVEL = 'flow'`
- 分批处理数据

### Q: 训练时间太长怎么办？
A: 可以：
- 减少 `EPOCHS` 数量
- 增加 `BATCH_SIZE` (如果内存允许)
- 使用GPU加速

### Q: 精度不够高怎么办？
A: 建议：
- 增加训练样本数量
- 调整学习率
- 检查数据质量
- 尝试不同的特征组合

## 📞 获取帮助

1. 查看详细文档: `CUSTOM_DATASET_GUIDE.md`
2. 运行环境测试: `python test_setup.py`
3. 检查配置: `python config_template.py`

## 🔄 工作流程

```mermaid
graph TD
    A[准备PCAP数据] --> B[配置参数]
    B --> C[环境测试]
    C --> D[数据预处理]
    D --> E[特征提取]
    E --> F[格式转换]
    F --> G[模型训练]
    G --> H[模型推理]
    H --> I[结果分析]
```

## 📝 示例用法

### 恶意软件分类
```bash
python run_custom_dataset.py \
    --input_path "/data/malware/" \
    --num_classes 5 \
    --samples_per_class 500 \
    --dataset_level flow
```

### 应用程序识别
```bash
python run_custom_dataset.py \
    --input_path "/data/apps/" \
    --num_classes 20 \
    --samples_per_class 1000 \
    --dataset_level packet
```

---

**注意**: 请确保您的数据使用符合相关法律法规，并已获得适当授权。
