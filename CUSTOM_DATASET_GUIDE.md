# ET-BERT 自定义数据集适配指南

本指南将帮助您将自己的PCAP数据集适配到ET-BERT模型中进行流量分类。

## 📋 目录

1. [环境准备](#环境准备)
2. [数据准备](#数据准备)
3. [配置设置](#配置设置)
4. [运行流程](#运行流程)
5. [常见问题](#常见问题)
6. [高级用法](#高级用法)

## 🛠️ 环境准备

### 1. 安装Python依赖

```bash
pip install -r requirements.txt
pip install scapy==2.4.4 numpy==1.19.2 scikit-learn pandas tqdm
```

### 2. 安装必需工具

#### Windows系统:
- **tshark**: 下载并安装 [Wireshark](https://www.wireshark.org/download.html)
- **SplitCap**: 下载 [SplitCap](https://www.netresec.com/?page=SplitCap)

#### Linux系统:
```bash
sudo apt-get install tshark
# SplitCap需要在Wine环境下运行，或使用替代工具
```

### 3. 下载预训练模型

```bash
# 下载ET-BERT预训练模型
wget -O pretrained_model.bin https://drive.google.com/file/d/1r1yE34dU2W8zSqx1FkB8gCWri4DQWVtE/view?usp=sharing
# 将模型文件放到 models/ 目录下
```

## 📁 数据准备

### 数据组织方式

您的PCAP数据可以按以下两种方式组织：

#### 方式1: 按类别分文件夹 (推荐)
```
your_pcap_data/
├── malware_type1/
│   ├── sample1.pcap
│   ├── sample2.pcap
│   └── ...
├── malware_type2/
│   ├── sample1.pcap
│   ├── sample2.pcap
│   └── ...
└── normal_traffic/
    ├── sample1.pcap
    ├── sample2.pcap
    └── ...
```

#### 方式2: 所有文件在一个文件夹
```
your_pcap_data/
├── sample1.pcap
├── sample2.pcap
├── sample3.pcapng
└── ...
```
*注意: 这种方式会被自动归类为单一类别*

### 支持的文件格式
- `.pcap` 文件
- `.pcapng` 文件 (会自动转换为pcap格式)

## ⚙️ 配置设置

### 1. 修改配置文件

编辑 `config_template.py` 文件，修改以下关键参数：

```python
# 基本路径配置
PCAP_INPUT_PATH = "D:/your_pcap_data/"  # 您的PCAP文件路径
OUTPUT_PATH = "D:/et_bert_output/"      # 输出路径

# 数据集参数
NUM_CLASSES = 5                         # 您的数据集类别数量
SAMPLES_PER_CLASS = 1000               # 每个类别的样本数量
DATASET_LEVEL = 'packet'               # 'packet' 或 'flow'
```

### 2. 配置验证

运行配置验证：
```bash
python config_template.py
```

## 🚀 运行流程

### 方法1: 一键运行 (推荐)

```bash
# 运行完整流程 (数据预处理 + 训练 + 推理)
python run_custom_dataset.py --step all

# 或者指定具体参数
python run_custom_dataset.py \
    --input_path "D:/your_pcap_data/" \
    --output_path "D:/output/" \
    --num_classes 5 \
    --samples_per_class 1000 \
    --step all
```

### 方法2: 分步执行

#### 步骤1: 数据预处理
```bash
python run_custom_dataset.py --step preprocess
```

#### 步骤2: 模型训练
```bash
python run_custom_dataset.py --step train
```

#### 步骤3: 模型推理
```bash
python run_custom_dataset.py --step inference
```

### 方法3: 使用适配器脚本

```bash
python custom_dataset_adapter.py \
    --input_path "D:/your_pcap_data/" \
    --output_path "D:/output/" \
    --num_classes 5 \
    --samples_per_class 1000 \
    --dataset_level packet
```

## 📊 输出结果

运行完成后，您将得到以下文件：

```
output_directory/
├── splitcap/                    # 分割后的PCAP文件
├── dataset/                     # 生成的numpy数据集
├── tsv_files/                   # TSV格式训练数据
│   ├── train_dataset.tsv
│   ├── valid_dataset.tsv
│   ├── test_dataset.tsv
│   └── nolabel_test_dataset.tsv
├── dataset.json                 # 原始特征数据
└── prediction.tsv               # 预测结果
```

## ❓ 常见问题

### Q1: "SplitCap.exe 不是内部或外部命令"
**解决方案**: 
- 下载SplitCap并将其路径添加到系统PATH
- 或在配置文件中指定完整路径

### Q2: 内存不足错误
**解决方案**:
- 减少 `SAMPLES_PER_CLASS` 的值
- 减少 `PAYLOAD_LENGTH` 的值
- 分批处理数据

### Q3: 某些PCAP文件无法处理
**解决方案**:
- 检查文件是否损坏
- 确保文件包含足够的数据包
- 调整 `MIN_FILE_SIZE_KB` 参数

### Q4: 训练精度不高
**解决方案**:
- 增加训练样本数量
- 调整学习率和训练轮数
- 检查数据质量和标签正确性

## 🔧 高级用法

### 自定义特征提取

修改配置文件中的 `FEATURES` 参数：

```python
# 只使用载荷数据
FEATURES = ['payload']

# 使用多种特征
FEATURES = ['payload', 'length', 'time', 'direction', 'message_type']
```

### 调整模型参数

```python
# 训练参数调优
BATCH_SIZE = 16          # 减少批次大小以节省内存
EPOCHS = 20              # 增加训练轮数
LEARNING_RATE = 1e-5     # 降低学习率
SEQUENCE_LENGTH = 256    # 增加序列长度
```

### 使用自定义配置文件

```bash
# 创建自定义配置文件
cp config_template.py my_config.py
# 编辑 my_config.py

# 使用自定义配置运行
python run_custom_dataset.py --config_file my_config.py
```

## 📈 性能优化建议

1. **数据预处理优化**:
   - 使用SSD存储以提高I/O性能
   - 并行处理多个PCAP文件
   - 预先过滤无用的网络流量

2. **训练优化**:
   - 使用GPU加速训练
   - 调整批次大小以充分利用GPU内存
   - 使用混合精度训练

3. **内存优化**:
   - 分批加载数据
   - 及时清理中间文件
   - 使用数据生成器而非一次性加载

## 🔍 调试技巧

### 启用详细日志
```bash
python run_custom_dataset.py --step preprocess --verbose
```

### 检查中间结果
```python
# 检查数据集统计信息
import json
with open('output/dataset.json', 'r') as f:
    dataset = json.load(f)
    
for label, data in dataset.items():
    print(f"类别 {label}: {data['samples']} 个样本")
```

### 验证TSV文件格式
```bash
head -5 output/tsv_files/train_dataset.tsv
```

## 📞 获取帮助

如果遇到问题，请：

1. 检查错误日志和输出信息
2. 确认配置参数是否正确
3. 验证输入数据格式
4. 查看GitHub Issues或提交新问题

## 🎯 快速开始示例

### 示例1: 恶意软件流量分类

假设您有5种恶意软件的PCAP文件：

```bash
# 1. 组织数据
mkdir -p /data/malware_pcaps/{trojan,ransomware,botnet,adware,normal}
# 将对应的PCAP文件放入各自文件夹

# 2. 修改配置
# 编辑 config_template.py:
PCAP_INPUT_PATH = "/data/malware_pcaps/"
NUM_CLASSES = 5
SAMPLES_PER_CLASS = 500

# 3. 运行处理
python run_custom_dataset.py --step all
```

### 示例2: 应用程序流量分类

```bash
# 处理应用程序流量数据
python run_custom_dataset.py \
    --input_path "/data/app_traffic/" \
    --output_path "/data/app_output/" \
    --num_classes 10 \
    --samples_per_class 1000 \
    --step all
```

## 📋 检查清单

在运行脚本之前，请确认：

- [ ] 已安装所有依赖工具 (tshark, SplitCap)
- [ ] 已下载预训练模型到 models/ 目录
- [ ] PCAP文件已按类别组织
- [ ] 配置文件中的路径正确
- [ ] 有足够的磁盘空间存储中间文件
- [ ] 系统内存足够处理数据集

---

**注意**: 请确保您的PCAP数据符合相关法律法规要求，并且已获得适当的使用授权。
