# ET-BERT 样本提取策略说明

## 🔍 问题分析

### 原始问题
您的数据集结构：
- 18个类别
- 每个类别有1-7个PCAP文件
- 每个PCAP文件包含上万个数据包
- 之前的处理方式：1个PCAP文件 = 1个样本（总共只有45个样本）

### 问题所在
1. **样本数量严重不足**：45个样本无法训练深度学习模型
2. **数据利用率极低**：每个PCAP文件的上万个数据包只被当作一个样本
3. **类别不平衡**：有些类别只有1-2个样本

## 🔧 新的解决方案

### 包级别处理（Packet-Level）
现在的处理方式：
- **1个数据包 = 1个样本**
- 从每个PCAP文件中提取多个数据包作为独立样本
- 每个类别目标生成1000个样本

### 具体实现
```python
# 对于每个PCAP文件
packets = scapy.rdpcap(pcap_file)  # 读取所有数据包

# 如果数据包太多，随机采样
if len(packets) > max_samples_per_file:
    selected_packets = random.sample(packets, max_samples_per_file)

# 为每个数据包生成一个样本
for packet in selected_packets:
    packet_feature = extract_packet_features(packet)
    samples.append(packet_feature)
```

## 📊 预期结果

### 样本数量对比
| 类别 | PCAP文件数 | 旧方式样本数 | 新方式样本数 |
|------|------------|--------------|--------------|
| camera | 7 | 7 | 1000 |
| POWER | 5 | 5 | 1000 |
| abnormal | 4 | 4 | 1000 |
| DCS | 3 | 3 | 1000 |
| ... | ... | ... | 1000 |
| **总计** | **45** | **45** | **18000** |

### 数据集分割
- **训练集**：70% ≈ 12,600 样本
- **验证集**：15% ≈ 2,700 样本  
- **测试集**：15% ≈ 2,700 样本

## 🎯 样本提取策略

### 1. 智能采样
```python
# 每个类别目标样本数
target_samples = 1000

# 每个PCAP文件的样本分配
samples_per_file = target_samples // num_pcap_files

# 如果PCAP文件数据包太多，随机采样
if total_packets > samples_per_file:
    selected_packets = random.sample(packets, samples_per_file)
```

### 2. 特征提取
```python
# 对每个数据包
for packet in packets:
    # 提取载荷数据
    payload = extract_payload(packet)
    
    # 转换为十六进制
    hex_data = binascii.hexlify(payload)
    
    # 生成bigram特征
    features = bigram_generation(hex_data, payload_length)
```

### 3. 质量控制
- 过滤空载荷的数据包
- 确保最小载荷长度
- 去除重复或无效的样本

## 🔄 处理流程

### 步骤1：扫描数据集
```
📁 扫描 /home/<USER>/NetMamba/dataset/database_clean/
├── camera/ (7个PCAP文件)
├── POWER/ (5个PCAP文件)
└── ...
```

### 步骤2：样本分配
```
🎯 每个类别目标：1000个样本
📊 camera类别：1000样本 ÷ 7文件 ≈ 143样本/文件
📊 POWER类别：1000样本 ÷ 5文件 = 200样本/文件
```

### 步骤3：特征提取
```
📦 处理 camera/file1.pcap
    📊 读取到 15,000 个数据包
    🎲 随机选择 143 个数据包
    ✅ 生成 143 个样本

📦 处理 camera/file2.pcap
    📊 读取到 12,000 个数据包
    🎲 随机选择 143 个数据包
    ✅ 生成 143 个样本
```

## ⚙️ 配置参数

### 在 data_process/main.py 中调整：
```python
# 每个类别的目标样本数
samples = [1000]  # 可以调整为 [500], [2000] 等

# 载荷长度
payload_length = 128  # 每个样本的特征长度

# 数据级别
dataset_level = "packet"  # 包级别处理
```

### 高级配置：
```python
# 每个文件最大样本数（避免内存问题）
max_samples_per_file = 1000

# 最小载荷长度（过滤小包）
min_payload_length = 20

# 随机种子（确保可重现）
random.seed(40)
```

## 🚀 运行新版本

```bash
# 重新运行数据处理
python run_netmamba_dataset.py --step preprocess
```

### 预期输出：
```
🔄 处理类别: camera
  📁 类别 camera: 发现 7 个PCAP文件
  🎯 目标样本数: 1000
  📦 处理文件 1/7: file1.pcap
    📊 读取到 15000 个数据包
    🎲 随机选择 143 个数据包
    ✅ 从文件生成了 143 个样本
  ...
  ✅ 类别 camera 处理完成: 1000 个有效样本
```

## 📈 优势

1. **充分利用数据**：每个数据包都可能成为训练样本
2. **样本数量充足**：18,000个样本足够训练深度学习模型
3. **类别平衡**：每个类别都有相同数量的样本
4. **随机性**：随机采样增加数据多样性
5. **可扩展性**：可以根据需要调整样本数量

## 🔍 验证方法

### 检查生成的数据：
```python
import json
with open('/home/<USER>/ET-BERT/output/result/dataset.json', 'r') as f:
    dataset = json.load(f)

for label, data in dataset.items():
    print(f"类别 {label}: {data['samples']} 个样本")
```

### 预期结果：
```
类别 0: 1000 个样本
类别 1: 1000 个样本
...
类别 17: 1000 个样本
```

这样就能充分利用您的数据集，生成足够的样本进行有效的模型训练！
