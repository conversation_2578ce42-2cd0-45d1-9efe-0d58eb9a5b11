#!/usr/bin/python3
# -*- coding:utf-8 -*-
"""
分析ET-BERT训练和预测结果
"""

import os
import pandas as pd
import numpy as np
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import json

def load_class_mapping():
    """加载类别映射"""
    dataset_path = "/home/<USER>/ET-BERT/output/result/dataset.json"
    pcap_path = "/home/<USER>/NetMamba/dataset/database_clean/"
    
    # 从原始数据集获取类别名称
    class_names = []
    if os.path.exists(pcap_path):
        for item in sorted(os.listdir(pcap_path)):
            item_path = os.path.join(pcap_path, item)
            if os.path.isdir(item_path):
                class_names.append(item)
    
    # 创建ID到名称的映射
    class_mapping = {i: name for i, name in enumerate(class_names)}
    
    return class_mapping

def analyze_predictions():
    """分析预测结果"""
    print("📊 分析预测结果...")

    # 检查多个可能的预测文件位置
    possible_prediction_paths = [
        "/home/<USER>/ET-BERT/output/prediction.tsv",
        "/home/<USER>/ET-BERT/prediction.tsv",
        "prediction.tsv",
        "/home/<USER>/ET-BERT/output/result/prediction.tsv"
    ]

    prediction_path = None
    for path in possible_prediction_paths:
        if os.path.exists(path):
            prediction_path = path
            print(f"✅ 找到预测文件: {path}")
            break

    if prediction_path is None:
        print("❌ 在以下位置都没有找到预测文件:")
        for path in possible_prediction_paths:
            print(f"  - {path}")

        # 列出实际存在的文件
        print("\n🔍 检查实际存在的文件:")
        output_dirs = ["/home/<USER>/ET-BERT/output/", "/home/<USER>/ET-BERT/", "."]
        for dir_path in output_dirs:
            if os.path.exists(dir_path):
                print(f"  📁 {dir_path}:")
                try:
                    files = [f for f in os.listdir(dir_path) if f.endswith('.tsv')]
                    if files:
                        for file in files:
                            print(f"    - {file}")
                    else:
                        print("    (没有.tsv文件)")
                except:
                    print("    (无法访问)")
        return False

    test_data_path = "/home/<USER>/ET-BERT/output/datasets/test_dataset.tsv"

    if not os.path.exists(test_data_path):
        print(f"❌ 测试数据文件不存在: {test_data_path}")
        return False
    
    if not os.path.exists(test_data_path):
        print(f"❌ 测试数据文件不存在: {test_data_path}")
        return False
    
    try:
        # 读取预测结果
        print("📖 读取预测结果...")
        predictions_df = pd.read_csv(prediction_path, sep='\t')
        print(f"  预测结果形状: {predictions_df.shape}")
        print(f"  列名: {list(predictions_df.columns)}")
        
        # 读取测试数据（包含真实标签）
        print("📖 读取测试数据...")
        test_df = pd.read_csv(test_data_path, sep='\t')
        print(f"  测试数据形状: {test_df.shape}")
        print(f"  列名: {list(test_df.columns)}")
        
        # 检查数据长度是否匹配
        if len(predictions_df) != len(test_df):
            print(f"⚠️  数据长度不匹配:")
            print(f"    预测结果: {len(predictions_df)} 行")
            print(f"    测试数据: {len(test_df)} 行")
            # 取较短的长度
            min_len = min(len(predictions_df), len(test_df))
            predictions_df = predictions_df.head(min_len)
            test_df = test_df.head(min_len)
            print(f"    截取到: {min_len} 行")
        
        # 提取真实标签和预测标签
        if 'label' in test_df.columns:
            true_labels = test_df['label'].values
        else:
            print("❌ 测试数据中没有找到'label'列")
            return False
        
        # 预测结果的列名可能不同，尝试几种可能的列名
        pred_column = None
        possible_columns = ['prediction', 'predicted_label', 'label', '0']
        
        for col in possible_columns:
            if col in predictions_df.columns:
                pred_column = col
                break
        
        if pred_column is None:
            print(f"❌ 预测文件中没有找到预测列，可用列: {list(predictions_df.columns)}")
            # 如果只有一列，就使用第一列
            if len(predictions_df.columns) == 1:
                pred_column = predictions_df.columns[0]
                print(f"  使用第一列作为预测结果: {pred_column}")
            else:
                return False
        
        predicted_labels = predictions_df[pred_column].values
        
        print(f"✅ 数据加载完成:")
        print(f"  样本数量: {len(true_labels)}")
        print(f"  真实标签范围: {min(true_labels)} - {max(true_labels)}")
        print(f"  预测标签范围: {min(predicted_labels)} - {max(predicted_labels)}")
        
        return true_labels, predicted_labels
        
    except Exception as e:
        print(f"❌ 分析预测结果时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def calculate_metrics(true_labels, predicted_labels):
    """计算各种评估指标"""
    print("\n📈 计算评估指标...")
    
    # 基本准确率
    accuracy = accuracy_score(true_labels, predicted_labels)
    print(f"🎯 总体准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    # 获取类别映射
    class_mapping = load_class_mapping()
    class_names = [class_mapping.get(i, f"Class_{i}") for i in range(max(max(true_labels), max(predicted_labels)) + 1)]
    
    # 详细分类报告
    print("\n📋 详细分类报告:")
    report = classification_report(true_labels, predicted_labels, 
                                 target_names=class_names, 
                                 output_dict=True)
    
    # 打印每个类别的指标
    print(f"{'类别':<20} {'精确率':<10} {'召回率':<10} {'F1分数':<10} {'样本数':<10}")
    print("-" * 70)
    
    for i, class_name in enumerate(class_names):
        if str(i) in report:
            metrics = report[str(i)]
            print(f"{class_name:<20} {metrics['precision']:<10.4f} {metrics['recall']:<10.4f} {metrics['f1-score']:<10.4f} {metrics['support']:<10.0f}")
    
    # 宏平均和加权平均
    print("-" * 70)
    macro_avg = report['macro avg']
    weighted_avg = report['weighted avg']
    print(f"{'宏平均':<20} {macro_avg['precision']:<10.4f} {macro_avg['recall']:<10.4f} {macro_avg['f1-score']:<10.4f}")
    print(f"{'加权平均':<20} {weighted_avg['precision']:<10.4f} {weighted_avg['recall']:<10.4f} {weighted_avg['f1-score']:<10.4f}")
    
    return accuracy, report

def analyze_confusion_matrix(true_labels, predicted_labels):
    """分析混淆矩阵"""
    print("\n🔍 混淆矩阵分析...")
    
    cm = confusion_matrix(true_labels, predicted_labels)
    class_mapping = load_class_mapping()
    
    print("混淆矩阵 (行=真实标签, 列=预测标签):")
    
    # 打印类别名称作为列标题
    class_names = [class_mapping.get(i, f"C{i}") for i in range(len(cm))]
    header = "真实\\预测".ljust(15) + "".join([f"{name[:8]:<10}" for name in class_names])
    print(header)
    print("-" * len(header))
    
    for i, row in enumerate(cm):
        class_name = class_mapping.get(i, f"Class_{i}")
        row_str = f"{class_name[:14]:<15}" + "".join([f"{val:<10}" for val in row])
        print(row_str)
    
    # 找出最容易混淆的类别对
    print("\n🔍 最容易混淆的类别对:")
    confusion_pairs = []
    for i in range(len(cm)):
        for j in range(len(cm)):
            if i != j and cm[i][j] > 0:
                confusion_pairs.append((i, j, cm[i][j]))
    
    # 按混淆次数排序
    confusion_pairs.sort(key=lambda x: x[2], reverse=True)
    
    for i, (true_class, pred_class, count) in enumerate(confusion_pairs[:10]):
        true_name = class_mapping.get(true_class, f"Class_{true_class}")
        pred_name = class_mapping.get(pred_class, f"Class_{pred_class}")
        print(f"  {i+1}. {true_name} -> {pred_name}: {count} 次")

def analyze_dataset_info():
    """分析数据集信息"""
    print("\n📊 数据集信息:")
    
    # 读取训练、验证、测试数据集
    datasets = {
        "训练集": "/home/<USER>/ET-BERT/output/datasets/train_dataset.tsv",
        "验证集": "/home/<USER>/ET-BERT/output/datasets/valid_dataset.tsv", 
        "测试集": "/home/<USER>/ET-BERT/output/datasets/test_dataset.tsv"
    }
    
    total_samples = 0
    for name, path in datasets.items():
        if os.path.exists(path):
            df = pd.read_csv(path, sep='\t')
            samples = len(df)
            total_samples += samples
            print(f"  {name}: {samples:,} 个样本")
            
            # 显示类别分布
            if 'label' in df.columns:
                class_dist = df['label'].value_counts().sort_index()
                print(f"    类别分布: {dict(class_dist)}")
        else:
            print(f"  {name}: 文件不存在")
    
    print(f"  总计: {total_samples:,} 个样本")

def main():
    print("🚀 ET-BERT 结果分析")
    print("="*60)
    
    # 分析数据集信息
    analyze_dataset_info()
    
    # 分析预测结果
    result = analyze_predictions()
    if not result:
        print("❌ 无法分析预测结果")
        return 1
    
    true_labels, predicted_labels = result
    
    # 计算评估指标
    accuracy, report = calculate_metrics(true_labels, predicted_labels)
    
    # 分析混淆矩阵
    analyze_confusion_matrix(true_labels, predicted_labels)
    
    # 保存结果报告
    print(f"\n💾 保存结果报告...")
    report_path = "/home/<USER>/ET-BERT/output/evaluation_report.txt"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(f"ET-BERT 模型评估报告\n")
        f.write(f"="*50 + "\n\n")
        f.write(f"总体准确率: {accuracy:.4f} ({accuracy*100:.2f}%)\n\n")
        f.write(f"详细分类报告:\n")
        f.write(str(report))
    
    print(f"  报告已保存到: {report_path}")
    
    print(f"\n🎉 分析完成!")
    print(f"📊 主要结果:")
    print(f"  🎯 准确率: {accuracy*100:.2f}%")
    print(f"  📁 详细报告: {report_path}")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
